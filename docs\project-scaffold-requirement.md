# 浏览器插件脚手架核心模块 - 技术规格与设计文档

**版本**: 1.0
**状态**: 定稿

## 1. 概述与设计原则

### 1.1. 核心目标
本脚手架旨在为浏览器插件提供一个统一的、自动化的多渠道开发与发布解决方案。核心是解决**单一代码库 (Single Codebase)** 到 **多个发布目标 (Multiple Targets)** 的工程化挑战，涵盖不同浏览器、不同 Manifest 版本及不同功能变体。

### 1.2. 设计原则
*   **单一数据源 (SSoT)**: 所有配置的权威来源是 `extension.config.ts`。禁止在代码中硬编码配置。
*   **配置即代码 (CaC)**: 利用 TypeScript 的类型系统提供智能提示和静态校验，提升配置体验和安全性。
*   **关注点分离 (SoC)**: 各模块职责明确。`config` 模块负责编排，`i18n` 模块负责语言，`manifest` 模块负责清单。
*   **数据与I/O分离**: 核心逻辑模块只负责处理数据并返回数据结构，不执行任何文件系统写入操作。这使得核心逻辑易于测试和复用。
*   **可组合性**: 功能被拆分为独立的、可复用的函数，由上层流程按需组合调用。

## 2. 文件结构

项目的目录结构经过精心设计，旨在将 **源码 (Source)**、**共享资源 (Shared)**、**自动化脚本 (Scripts)** 和 **构建产物 (Artifacts)** 清晰地分离开来。

```
.
├── .output/{extensionName}/{version}/{variantTarget}-dev/ # [构建产物] WXT 的输出目录 (开发环境)
├── .output/{extensionName}/{version}/{variantTarget}/ # [构建产物] WXT 的输出目录 (生产环境)
├── packages/
│   ├── extensions/
│   │   └── {extensionName}/
│   │       ├── .manifest/            # [构建产物] 各渠道包最终 manifest.json 的备份
│   │       │   └── manifest.{variantTarget}.json
│   │       │
│   │       ├── .variants/            # [构建产物] 各渠道包的中间配置产物和资源
│   │       │   ├── {variantTarget}/
│   │       │   │   ├── public/
│   │       │   │   │   └── _locales/
│   │       │   │   │       └── {lang}/
│   │       │   │   │           └── messages.json
│   │       │   │   ├── i18n.json     # [产物] 为 vue-i18n 生成的语言包
│   │       │   │   └── extension.json# [产物] 该渠道包的最终合并配置
│   │       │
│   │       ├── extension.config.ts   # [源码] 插件的唯一配置文件
│   │       ├── locales/              # [源码] 插件专属语言包 (e.g., en.json)
│   │       ├── icons/                # [源码] 插件图标
│   │       └── ...                   # [源码] 插件的其他源代码 (Vue, TS, etc.)
│   │
│   └── shared/
│       └── locales/                  # [共享源码] 跨插件共享的语言包
│
└── scripts/
    ├── extension-config/             # [脚本] 配置管理模块
    ├── i18n/                         # [脚本] 国际化处理模块
    ├── manifest/                     # [脚本] Manifest 处理模块
    └── build/                        # [脚本] 构建流程主入口，负责调用其他模块并写入文件
    └── utils/                        # [脚本] 工具函数库
```

### 2.1. 目录职责详解

*   `packages/extensions/{extensionName}`: **插件源码目录**
    *   这是开发者主要工作的区域，包含了插件的所有业务逻辑和资源。
    *   **`.variants/` (自动生成)**:
        *   **用途**: 存放每个渠道包 (`variantTarget`) 在构建过程中生成的**中间产物**。这些产物是构建流程（如 `wxt`）的直接输入。
        *   **`extension.json`**: 由 `scripts/extension-config` 模块生成的、包含所有信息的最终数据结构。它是所有其他产物的“信源”。
        *   **`i18n.json`**: 从 `extension.json` 提取并转换，供 `vue-i18n` 在前端界面使用。
        *   **`public/_locales/`**: 从 `extension.json` 提取并转换，供浏览器自身（如 `manifest.json` 中的 `__MSG__` 引用）使用。
        *   **重要**: 此目录应被添加到 `.gitignore` 中，因为它完全由自动化脚本生成，不应手动修改或提交到版本控制。
    *   **`.manifest/` (自动生成)**:
        *   **用途**: 存放每个渠道包最终生成的 `manifest.json` 的一份**可读备份**。
        *   **价值**: 这为开发者提供了一个快速审查和调试不同渠道包 `manifest` 差异的便捷途径，而无需每次都去解压构建好的插件包。

*   `packages/shared/`: **共享资源目录**
    *   存放可在多个插件间复用的代码或资源，如通用的语言包、工具函数库等。

*   `scripts/`: **自动化脚本目录**
    *   包含所有实现脚手架“魔法”的 Node.js/TypeScript 脚本。这些脚本是整个自动化流程的核心。

### 2.2. 数据流与文件生成的物理路径

1.  开发者修改 `packages/extensions/{extensionName}/extension.config.ts`。
2.  执行构建命令 `pnpm build --target=chrome-mv3-master`。
3.  `scripts/build` 脚本调用 `scripts/extension-config` 模块。
4.  `extension-config` 模块处理后，生成 `extension.json` 的**数据结构**。
5.  `scripts/build` 脚本接收这个数据结构，并执行文件写入操作：
    *   将完整数据写入 `packages/extensions/{extensionName}/.variants/{variantTarget}/extension.json`。
    *   从数据中提取 i18n 信息，写入 `.../.variants/{variantTarget}/i18n.json` 和 `.../.variants/{variantTarget}/public/_locales/...`。
6.  `wxt` 或其他构建工具启动，它的配置被动态指向 `.variants/{variantTarget}/` 目录，读取 `extension.json` 或 `manifest.json` 等中间产物，完成最终的打包。
7.  在 `wxt` 打包完成后，`scripts/build` 脚本可以将最终生成的 `manifest.json` 从 `.output/{extensionName}/{version}/{variantTarget}/manifest.json` 目录中复制一份到 `packages/extensions/{extensionName}/.manifest/manifest.{variantTarget}.json` 作为留底。


## 3. 统一配置管理 (`extension.config.ts`)

### 3.1. `defineExtensionConfig` 函数
开发者**必须**通过调用此函数来定义插件配置。此函数不仅提供类型支持，还执行初步的配置处理。

*   **输入**: `UserExtensionConfig` - 开发者编写的配置对象。
*   **处理逻辑**:
    1.  遍历 `userConfig.variants` 数组。
    2.  对于每个 variant，使用 `lodash-es/deepMerge` 将全局配置与当前 variant 的配置合并。
    3.  **规范化与自动填充**:
        *   将合并后的顶层 `manifestVersion` 和 `defaultLocale` 写入其 `manifest` 对象内部。
        *   自动计算并填充 `variantTarget`，格式为: `{webstore}-mv{manifestVersion}-{variantType}`。
        *   如果 `variantChannel` 或 `webstoreCN` 为空，则根据规则自动填充。
    4.  **校验**:
        *   检查 `name` 和 `version` 是否已在顶层定义。
        *   检查 `variants` 中每个对象的必填字段 (`variantId`, `variantName`, `variantType`, `webstore`)。
        *   确保生成的 `variantTarget` 是唯一的，防止构建冲突。
        *   如果用户填写了 `i18n.locales`，清空并发出警告，因为此字段由脚本自动扫描生成。
*   **返回值**: `ProcessedExtensionConfig` - 一个经过处理和校验的配置对象，作为所有后续流程的输入。此对象的结构是明确且规范化的。

### **`ProcessedExtensionConfig` 接口详解**

`ProcessedExtensionConfig` 接口代表了经过 `defineExtensionConfig` 函数处理后的最终配置结构。它的主要特点是：所有的变体（variants）都已经被“扁平化”，每个变体对象都包含了从全局配置继承并与自身配置合并后的**全量信息**。

```typescript
/**
 * 经过 defineExtensionConfig 处理后的配置对象结构。
 */
interface ProcessedExtensionConfig {
  /**
   * 插件项目名称，直接从用户配置中获取。
   */
  name: string;

  /**
   * 插件的版本号，直接从用户配置中获取。
   */
  version: string;

  /**
   * 一个处理过的渠道包 (变体) 对象数组。
   * 每个对象都是一个独立的、完整的配置单元。
   */
  variants: ProcessedVariantConfig[];
}

/**
 * 单个渠道包 (变体) 的完整配置结构。
 * 它继承了全局配置，并应用了自己的特定配置。
 * 所有可选字段都已被填充或赋予了确定的默认值。
 */
interface ProcessedVariantConfig {
  // --- 从全局继承或自身定义的元数据 ---
  name: string;
  version: string;
  
  // --- 被规范化和填充的 Variant 核心字段 ---
  variantId: string;
  variantName: string;
  variantType: 'master' | 'tm' | 'tmBeta' | 'dba' | 'offline';
  variantChannel: string; // [已填充]
  variantTarget: string;  // [已填充]
  webstore: string;
  webstoreCN: string;     // [已填充]
  webstoreId?: string;
  webstoreUrl?: string;

  // --- 被规范化和填充的插件核心配置 ---
  manifestVersion: 2 | 3; // [已确定]
  defaultLocale: string;    // [已确定]
  measurementId?: string;

  // --- 完整的 i18n 配置 ---
  i18n: {
    locales: string[]; // [已扫描填充]
    includes?: string[];
    excludes?: string[];
    chromeLocalesOnly: string[]; // [已赋予默认值]
    chromeMessagesOnly: string[]; // [已赋予默认值]
  };

  // --- 完整的、合并后的 manifest 对象 ---
  manifest: Partial<Browser.runtime.Manifest> & {
    // [已强制写入] 这些字段的值与顶层的 manifestVersion 和 defaultLocale 一致
    manifest_version: 2 | 3;
    default_locale: string;
  };
}
```

#### 返回值结构的关键点说明：

1.  **扁平化和完整性**: `ProcessedExtensionConfig` 的核心是 `variants` 数组。与用户输入的 `UserExtensionConfig` 不同，这里的每个 `ProcessedVariantConfig` 对象都是**自包含和完整的**。下游模块在处理某个 variant 时，只需要接收这一个对象，无需再关心全局配置是什么。

2.  **字段的确定性**: 所有在 `UserExtensionConfig` 中可能是可选的、需要计算的字段，在这里都变成了**确定**的。
    *   `variantTarget`, `variantChannel`, `webstoreCN` 等都已被计算和填充。
    *   `manifestVersion` 和 `defaultLocale` 已经从全局或 variant 自身配置中解析出来，成为一个确定的值。
    *   `i18n.locales` 已经被实际扫描文件系统后填充。
    *   `i18n.chromeLocalesOnly` 和 `i18n.chromeMessagesOnly` 如果用户未提供，则会被赋予文档中定义的默认值。

3.  **数据冗余是设计意图**: `name` 和 `version` 字段在每个 `ProcessedVariantConfig` 对象中都存在。这种“冗余”是刻意为之的，它确保了每个 variant 对象的独立性，下游模块可以完全解耦，不依赖任何外部上下文。

4.  **Manifest 的规范化**: `ProcessedVariantConfig.manifest` 对象内部的 `manifest_version` 和 `default_locale` 字段已经被强制与顶层的 `manifestVersion` 和 `defaultLocale` 同步。这消除了信息不一致的可能性。


### 3.2. 配置项详解 (`UserExtensionConfig` 接口)

```typescript
interface UserExtensionConfig {
  /**
   * [必填] 插件项目名称，与 `packages/extensions/` 下的文件夹名一致。
   */
  name: string;

  /**
   * [必填] 插件的版本号，遵循 SemVer 规范。
   */
  version: string;

  /**
   * [可选] 默认的 manifest 版本号 (e.g., 3)。可被 variant 覆盖。
   */
  manifestVersion?: 2 | 3;

  /**
   * [可选] 插件的默认语言环境 (e.g., 'en')。可被 variant 覆盖。
   */
  defaultLocale?: string;

  /**
   * [可选] Google Analytics Measurement ID。可被 variant 覆盖。
   */
  measurementId?: string;
  
  /**
   * [可选] 国际化 (i18n) 处理配置。
   */
  i18n?: {
    /**
     * [只读] 插件支持的语言列表。由脚本自动扫描填充，用户无需填写。
     */
    readonly locales?: string[];

    /**
     * [可选] 包含的文案 key (支持正则字符串)。如果定义，则只有匹配的文案会被保留。
     */
    includes?: string[];

    /**
     * [可选] 排除的文案 key (支持正则字符串)。
     */
    excludes?: string[];

    /**
     * [可选] 仅生成到 `_locales` 目录的语言。这些语言的文案不会出现在给 vue-i18n 的产物中。
     * 默认值: ['en_US', 'en_GB', 'pt_BR', 'es_419']
     */
    chromeLocalesOnly?: string[];

    /**
     * [可选] 仅包含在 `_locales` 目录中的文案 key (支持正则字符串)。在 `includes`/`excludes` 过滤后生效。
     * 默认值: ['EXTENSION_NAME', 'EXTENSION_DESCRIPTION', '^context_menu_.*']
     */
    chromeMessagesOnly?: string[];
  };

  /**
   * [可选] 标准的 manifest.json 配置骨架。所有 variant 都会继承此配置。
   * 注意: version, manifest_version, default_locale, name, description 字段会被脚本自动处理，用户填写无效。
   */
  manifest?: Partial<Browser.runtime.Manifest>;
  
  /**
   * [必填] 渠道包 (变体) 定义数组。
   */
  variants: VariantConfig[];
}

interface VariantConfig extends Omit<UserExtensionConfig, 'name' | 'version' | 'variants'> {
  /**
   * [必填] 渠道包的唯一 ID。
   */
  variantId: string;
  
  /**
   * [必填] 渠道包的用户友好名称。
   */
  variantName: string;
  
  /**
   * [必填] 渠道包类型。
   * 可选值: 'master' | 'tm' | 'tmBeta' | 'dba' | 'offline'
   */
  variantType: 'master' | 'tm' | 'tmBeta' | 'dba' | 'offline';
  
  /**
   * [必填] 目标浏览器商店的简称。
   * 可选值: 'chrome' | 'firefox' | 'edge' | 'opera' | 'browser360' | 'safari' | 'adspower'
   */
  webstore: string;

  /**
   * [可选] 渠道代码名称。如果留空，脚本将自动填充。
   * 自动填充规则: 如果 `variantType` 是 'offline', 格式为 `{webstore}_offline`，否则为 `{webstore}`。
   */
  variantChannel?: string;

  /**
   * [可选] 目标浏览器的内部代码名称。如果留空，脚本将自动填充。
   * 自动填充规则 (示例):
   * - 'firefox' -> 'e-f'
   * - 'opera'   -> 'e-o'
   * - 'edge'    -> 'e-edge'
   * - 'chrome' -> 'e-c'
   * - 'browser360' -> 'e-360'
   * - 'safari' -> 'e-safari'
   * - 'adspower' -> 'e-ads'
   */
  webstoreCN?: string;
  
  /**
   * [可选] 插件在对应商店的 ID。
   */
  webstoreId?: string;

  /**
   * [可选] 插件在对应商店的 URL。
   */
  webstoreUrl?: string;

  /**
   * [只读] 渠道包的最终构建目标标识符。由脚本自动生成，用户无需填写。
   * 格式: `{webstore}-mv{manifestVersion}-{variantType}`
   */
  readonly variantTarget?: string;
}
```

## 4. `extension.json` - 最终配置产物

此文件是为**每个渠道包**单独生成的构建产物，包含了所有构建所需的信息。它由 `extension-config` 模块负责**生成其数据结构**。

### 4.1. `extension.json` 的数据结构
```json
{
  "name": "string",
  "version": "string",
  "manifestVersion": "number",
  "defaultLocale": "string",
  "measurementId": "string",
  "variantId": "string",
  "variantName": "string",
  "variantType": "string",
  "variantChannel": "string",
  "variantTarget": "string",
  "webstore": "string",
  "webstoreCN": "string",
  "webstoreId": "string",
  "webstoreUrl": "string",
  "i18n": {
    "locales": ["en", "ja"],
    "chromeMessages": {
      "en": {
        "EXTENSION_NAME": { "message": "My Extension" },
        "context_menu_search": { 
          "message": "Search for $term$",
          "placeholders": { "term": { "content": "$1" } }
        }
      },
      "ja": { ... }
    },
    "vueI18nMessages": {
      "en": {
        "EXTENSION_NAME": "My Extension",
        "context_menu_search": "Search for {term}"
      },
      "ja": { ... }
    }
  },
  "manifest": {
    "manifest_version": 3,
    "name": "__MSG_EXTENSION_NAME__",
    "description": "__MSG_EXTENSION_DESCRIPTION__",
    "version": "1.0.0",
    "default_locale": "en",
    ...
  }
}
```

### 4.2. `extension.json` 的生成逻辑
1.  **选择渠道包**: 从 `defineExtensionConfig` 返回的 `ProcessedExtensionConfig.variants` 中取出一个渠道包的配置对象 (`variantConfig`)。
2.  **处理 i18n**:
    *   调用 `i18n.processLocalesForVariant()`，传入 `variantConfig.name`, `variantConfig.variantTarget` 和 `variantConfig.i18n` 过滤配置。
    *   接收返回的 `{ chromeMessages, vueI18nMessages }`。
3.  **处理 manifest**:
    *   调用 `manifest.generateManifestForVariant()`，传入 `variantConfig.manifest` 和 `variantConfig` 中的元数据（`version`, `manifestVersion` 等）。
    *   接收返回的最终 `manifest` 对象。
4.  **组装**:
    *   创建一个新对象。
    *   将 `variantConfig` 中的所有顶层元数据（`name`, `version`, `variantId` 等）复制到新对象中。
    *   将处理后的 i18n 数据和 manifest 数据作为 `i18n` 和 `manifest` 字段添加到新对象中。
    *   **返回这个组装好的数据对象。**

## 5. `scripts/i18n` 模块详解

### 5.1. 源文件格式
*   标准的 Chrome i18n 格式。
*   **支持条件化文案**: 在文案对象中，`message` 字段为默认值，`{variantTarget}` 字段为特定渠道的覆盖值。

```json
{
  "context_menu_title": {
    "message": "Search with My Extension",
    "chrome-mv3-master": "Search with My Extension v3",
    "firefox-mv2-master": "Find on My Extension"
  }
}
```

### 5.2. 对外接口 (Public API)
*   `loadSharedLocales(): Promise<LocaleData>`
*   `loadExtensionLocales(extensionName: string): Promise<LocaleData>`
*   `getAvailableLocales(localesPath: string): Promise<string[]>`
*   `processLocalesForVariant(input: I18nInput): Promise<I18nOutput>`

### 5.3. `processLocalesForVariant` 核心逻辑
1.  **加载**: 调用 `loadSharedLocales` 和 `loadExtensionLocales` 并合并数据。
2.  **文案选择**: 遍历所有文案，如果存在 `variantTarget` 对应的 key，则使用其值，否则使用 `message` 的值。
3.  **过滤**: 应用 `includes`/`excludes` 规则。
4.  **生成 `chromeMessages`**:
    *   应用 `chromeMessagesOnly` 规则。
    *   自动生成缺失的 `placeholders` 对象。
5.  **生成 `vueI18nMessages`**:
    *   应用 `chromeLocalesOnly` 规则。
    *   扁平化数据结构。
    *   转换占位符 (`$placeholder$` -> `{placeholder}`）。

## 6. `scripts/manifest` 模块详解

### 6.1. 对外接口 (Public API)
*   `generateManifestForVariant(input: ManifestInput): FinalManifest`

### 6.2. `generateManifestForVariant` 核心逻辑
1.  **权威填充**: 使用 `input.variantInfo` 的 `version`, `manifestVersion`, `defaultLocale` 强制覆盖 `baseManifest` 中的对应字段。
2.  **国际化链接**: 强制将 `name`, `description` 等字段的值替换为 `__MSG_...__` 格式。

## 7. 开发注意事项
*   **模块独立性**: 开发 `i18n` 或 `manifest` 模块时，应假设其输入是符合接口定义的纯数据，不应有对 `extension-config` 模块内部实现的依赖。
*   **纯函数**: 尽可能将核心逻辑实现为纯函数。这对于可测试性和可预测性至关重要。
*   **错误处理**: 所有模块都应有健全的错误处理。例如，当 `glob` 找不到文件，或 JSON 解析失败时，应抛出有意义的错误。
*   **日志**: 使用 `consola` 或类似工具提供清晰的、带级别的日志输出（`info`, `warn`, `error`），方便调试构建过程。
*   **测试**: 必须为 `scripts/` 目录下的核心处理函数编写单元测试（使用 `vitest`），覆盖正常流程、边界情况和错误情况。
