/**
 * @fileoverview Extension configuration module constant definitions
 * @description Defines default values, path configurations, and various enumeration constants
 */

import type { DefaultValues } from './types.js';

/**
 * Default configuration values
 */
export const DEFAULT_VALUES: DefaultValues = {
  manifestVersion: 3,
  defaultLocale: 'en',
  chromeLocalesOnly: ['en_US', 'en_GB', 'pt_BR', 'es_419'],
  chromeMessagesOnly: ['EXTENSION_NAME', 'EXTENSION_DESCRIPTION', '^context_menu_.*'],
};

/**
 * Supported browser stores list
 */
export const SUPPORTED_WEBSTORES = [
  'chrome',
  'firefox', 
  'edge',
  'opera',
  'browser360',
  'safari',
  'adspower'
] as const;

/**
 * Supported variant types
 */
export const SUPPORTED_VARIANT_TYPES = [
  'master',
  'tm',
  'tmBeta', 
  'dba',
  'offline'
] as const;

/**
 * Supported Manifest versions
 */
export const SUPPORTED_MANIFEST_VERSIONS = [2, 3] as const;

/**
 * Browser store to internal code mapping
 */
export const WEBSTORE_TO_CN_MAPPING: Record<string, string> = {
  'chrome': 'e-c',
  'firefox': 'e-f',
  'edge': 'e-edge',
  'opera': 'e-o',
  'browser360': 'e-360',
  'safari': 'e-safari',
  'adspower': 'e-ads',
};

/**
 * Required configuration fields
 */
export const REQUIRED_FIELDS = {
  USER_CONFIG: ['name', 'version', 'variants'],
  VARIANT_CONFIG: ['variantId', 'variantName', 'variantType', 'webstore'],
} as const;

/**
 * Auto-generated fields list (users should not manually set these)
 */
export const AUTO_GENERATED_FIELDS = [
  'variantTarget',
  'variantChannel',
  'webstoreCN',
  'i18n.locales',
] as const;

/**
 * File path related constants
 */
export const FILE_PATHS = {
  EXTENSION_CONFIG: 'extension.config.ts',
  EXTENSION_JSON: 'extension.json',
  I18N_JSON: 'i18n.json',
  MANIFEST_JSON: 'manifest.json',
  VARIANTS_DIR: '.variants',
  MANIFEST_DIR: '.manifest',
  LOCALES_DIR: 'locales',
  SHARED_LOCALES_DIR: 'packages/shared/locales',
  CHROME_LOCALES_DIR: 'public/_locales',
} as const;

/**
 * Build related constants
 */
export const BUILD_CONSTANTS = {
  OUTPUT_DIR: '.output',
  TEMP_DIR: '.temp',
  NODE_ENV: {
    DEVELOPMENT: 'development',
    PRODUCTION: 'production',
  },
} as const;

/**
 * Log levels
 */
export const LOG_LEVELS = {
  DEBUG: 'debug',
  INFO: 'info',
  WARN: 'warn',
  ERROR: 'error',
} as const;

/**
 * Error message constants
 */
export const ERROR_MESSAGES = {
  MISSING_REQUIRED_FIELD: (field: string) => `Required field '${field}' is missing`,
  INVALID_VARIANT_TYPE: (type: string) => `Invalid variant type '${type}'. Must be one of: ${SUPPORTED_VARIANT_TYPES.join(', ')}`,
  INVALID_WEBSTORE: (webstore: string) => `Invalid webstore '${webstore}'. Must be one of: ${SUPPORTED_WEBSTORES.join(', ')}`,
  INVALID_MANIFEST_VERSION: (version: number) => `Invalid manifest version '${version}'. Must be one of: ${SUPPORTED_MANIFEST_VERSIONS.join(', ')}`,
  DUPLICATE_VARIANT_TARGET: (target: string) => `Duplicate variant target '${target}'. Each variant must have a unique target.`,
  CONFIG_VALIDATION_FAILED: 'Configuration validation failed',
  EXTENSION_NOT_FOUND: (name: string) => `Extension '${name}' not found`,
  LOCALE_FILE_NOT_FOUND: (path: string) => `Locale file not found: ${path}`,
  INVALID_JSON: (file: string) => `Invalid JSON format in file: ${file}`,
} as const;

/**
 * Success message constants
 */
export const SUCCESS_MESSAGES = {
  CONFIG_PROCESSED: 'Configuration processed successfully',
  FILES_GENERATED: 'Files generated successfully',
  VALIDATION_PASSED: 'Validation passed',
  BUILD_COMPLETED: 'Build completed successfully',
} as const;