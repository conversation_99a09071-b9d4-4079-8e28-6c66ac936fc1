/**
 * @fileoverview 配置管理模块常量定义
 * @description 定义默认值、映射表和各种枚举常量
 */

import type { DefaultValues, VariantType, WebstoreType, ManifestVersion } from './types.js';

/**
 * 默认配置值
 */
export const DEFAULT_VALUES: DefaultValues = {
  manifestVersion: 3,
  defaultLocale: 'en',
  chromeLocalesOnly: ['en_US', 'en_GB', 'pt_BR', 'es_419'],
  chromeMessagesOnly: ['EXTENSION_NAME', 'EXTENSION_DESCRIPTION', '^context_menu_.*'],
};

/**
 * 支持的浏览器商店列表
 */
export const SUPPORTED_WEBSTORES: readonly WebstoreType[] = [
  'chrome',
  'firefox',
  'edge',
  'opera',
  'browser360',
  'safari',
  'adspower',
] as const;

/**
 * 支持的变体类型列表
 */
export const SUPPORTED_VARIANT_TYPES: readonly VariantType[] = [
  'master',
  'tm',
  'tmBeta',
  'dba',
  'offline',
] as const;

/**
 * 支持的 Manifest 版本列表
 */
export const SUPPORTED_MANIFEST_VERSIONS: readonly ManifestVersion[] = [2, 3] as const;

/**
 * webstore 到 webstoreCN 的映射表
 */
export const WEBSTORE_TO_CN_MAPPING: Record<WebstoreType, string> = {
  chrome: 'e-c',
  firefox: 'e-f',
  edge: 'e-edge',
  opera: 'e-o',
  browser360: 'e-360',
  safari: 'e-safari',
  adspower: 'e-ads',
};

/**
 * 必填字段列表
 */
export const REQUIRED_FIELDS = {
  USER_CONFIG: ['name', 'version', 'variants'] as const,
  VARIANT_CONFIG: ['variantId', 'variantName', 'variantType', 'webstore'] as const,
} as const;

/**
 * 错误消息模板
 */
export const ERROR_MESSAGES = {
  MISSING_REQUIRED_FIELD: (field: string) => `缺少必填字段: ${field}`,
  INVALID_VARIANT_TYPE: (type: string) =>
    `不支持的变体类型: ${type}。支持的类型: ${SUPPORTED_VARIANT_TYPES.join(', ')}`,
  INVALID_WEBSTORE: (store: string) =>
    `不支持的浏览器商店: ${store}。支持的商店: ${SUPPORTED_WEBSTORES.join(', ')}`,
  INVALID_MANIFEST_VERSION: (version: number) =>
    `不支持的 Manifest 版本: ${version}。支持的版本: ${SUPPORTED_MANIFEST_VERSIONS.join(', ')}`,
  DUPLICATE_VARIANT_TARGET: (target: string) =>
    `重复的 variantTarget: ${target}。每个 variantTarget 必须唯一`,
  EMPTY_VARIANTS: () => '至少需要定义一个 variant',
  USER_PROVIDED_LOCALES: () => 'i18n.locales 字段由脚本自动扫描生成，用户提供的值将被忽略',
} as const;
