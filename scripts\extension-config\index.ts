/**
 * @fileoverview 配置管理模块主入口
 * @description 提供配置处理的核心 API
 *
 * 遵循文档中的设计原则：
 * - 单一数据源 (SSoT): 所有配置的权威来源是 extension.config.ts
 * - 配置即代码 (CaC): 利用 TypeScript 类型系统提供智能提示和静态校验
 * - 关注点分离 (SoC): 此模块只负责配置处理，不涉及 i18n 和 manifest 具体处理
 * - 数据与I/O分离: 核心逻辑只处理数据并返回数据结构，不执行文件系统写入操作
 * - 可组合性: 功能被拆分为独立的、可复用的函数
 */

import { createLogger } from '../utils/logger.js';
import type {
  UserExtensionConfig,
  ProcessedExtensionConfig,
  ProcessedVariantConfig,
} from './types.js';
import { validateUserConfig, checkUniqueVariantTargets } from './validators.js';
import { processVariantConfig } from './processors.js';

const logger = createLogger('ExtensionConfig');

/**
 * 主函数：定义和处理插件配置
 * 这是用户在 extension.config.ts 文件中调用的函数
 *
 * 遵循文档中的处理逻辑：
 * 1. 遍历 userConfig.variants 数组
 * 2. 对每个 variant，使用 lodash-es/merge 合并全局配置与当前 variant 配置
 * 3. 规范化与自动填充：
 *    - 将合并后的顶层 manifestVersion 和 defaultLocale 写入其 manifest 对象内部
 *    - 自动计算并填充 variantTarget，格式为: {webstore}-mv{manifestVersion}-{variantType}
 *    - 如果 variantChannel 或 webstoreCN 为空，则根据规则自动填充
 * 4. 校验：
 *    - 检查 name 和 version 是否已在顶层定义
 *    - 检查 variants 中每个对象的必填字段
 *    - 确保生成的 variantTarget 是唯一的，防止构建冲突
 *    - 如果用户填写了 i18n.locales，清空并发出警告
 *
 * @param userConfig - 用户输入的配置
 * @returns 处理后的配置对象
 */
export function defineExtensionConfig(userConfig: UserExtensionConfig): ProcessedExtensionConfig {
  logger.info('开始处理插件配置...');

  try {
    // 第4步：校验 - 检查 name 和 version 是否已在顶层定义
    validateUserConfig(userConfig);

    // 第1步：遍历 userConfig.variants 数组
    const processedVariants: ProcessedVariantConfig[] = [];
    const variantTargets = new Set<string>();

    for (const variant of userConfig.variants) {
      // 第2步：使用 lodash-es/merge 合并全局配置与当前 variant 配置
      // 第3步：规范化与自动填充
      const processedVariant = processVariantConfig(userConfig, variant);

      // 第4步：确保生成的 variantTarget 是唯一的，防止构建冲突
      checkUniqueVariantTargets(variantTargets, processedVariant.variantTarget);
      variantTargets.add(processedVariant.variantTarget);

      processedVariants.push(processedVariant);
    }

    const result: ProcessedExtensionConfig = {
      name: userConfig.name,
      version: userConfig.version,
      variants: processedVariants,
    };

    logger.info(`配置处理完成，共处理 ${processedVariants.length} 个变体`);
    logger.verbose('处理结果:', result);

    return result;
  } catch (error) {
    logger.error('配置处理失败:', error);
    throw error;
  }
}

// 导出类型定义，方便用户使用
export type {
  UserExtensionConfig,
  VariantConfig,
  ProcessedExtensionConfig,
  ProcessedVariantConfig,
  VariantType,
  WebstoreType,
  ManifestVersion,
  DefaultValues,
} from './types.js';

// 导出常量，方便其他模块使用
export {
  DEFAULT_VALUES,
  WEBSTORE_TO_CN_MAPPING,
  SUPPORTED_WEBSTORES,
  SUPPORTED_VARIANT_TYPES,
  SUPPORTED_MANIFEST_VERSIONS,
} from './constants';
