/**
 * @fileoverview 配置管理模块主入口
 * @description 提供配置处理的核心 API
 *
 * 遵循文档中的设计原则：
 * - 单一数据源 (SSoT): 所有配置的权威来源是 extension.config.ts
 * - 配置即代码 (CaC): 利用 TypeScript 类型系统提供智能提示和静态校验
 * - 关注点分离 (SoC): 此模块只负责配置处理，不涉及 i18n 和 manifest 具体处理
 * - 数据与I/O分离: 核心逻辑只处理数据并返回数据结构，不执行文件系统写入操作
 * - 可组合性: 功能被拆分为独立的、可复用的函数
 */

import fs from 'fs/promises';
import path from 'path';
const { resolve, join } = path;
import { createLogger } from '../utils/logger.js';
import type {
  UserExtensionConfig,
  ProcessedExtensionConfig,
  ProcessedVariantConfig,
  ListExtensionVariantsOptions,
  LightweightVariantInfo,
  GenerateExtensionConfigsOptions,
  FullVariantConfig,
} from './types.js';
import { validateUserConfig, checkUniqueVariantTargets } from './validators.js';
import { processVariantConfig } from './processors.js';
// TODO: 导入 i18n 和 manifest 模块（暂时注释掉，等模块完善后启用）
// import { processLocalesForVariant, getAvailableLocales } from '../i18n/index.js';
// import { generateManifestForVariant } from '../manifest/index.js';
// import type { I18nInput, ManifestInput } from './types.js';

const logger = createLogger('ExtensionConfig');

/**
 * 主函数：定义和处理插件配置
 * 这是用户在 extension.config.ts 文件中调用的函数
 *
 * 遵循文档中的处理逻辑：
 * 1. 遍历 userConfig.variants 数组
 * 2. 对每个 variant，使用 lodash-es/merge 合并全局配置与当前 variant 配置
 * 3. 规范化与自动填充：
 *    - 将合并后的顶层 manifestVersion 和 defaultLocale 写入其 manifest 对象内部
 *    - 自动计算并填充 variantTarget，格式为: {webstore}-mv{manifestVersion}-{variantType}
 *    - 如果 variantChannel 或 webstoreCN 为空，则根据规则自动填充
 * 4. 校验：
 *    - 检查 name 和 version 是否已在顶层定义
 *    - 检查 variants 中每个对象的必填字段
 *    - 确保生成的 variantTarget 是唯一的，防止构建冲突
 *    - 如果用户填写了 i18n.locales，清空并发出警告
 *
 * @param userConfig - 用户输入的配置
 * @returns 处理后的配置对象
 */
export function defineExtensionConfig(userConfig: UserExtensionConfig): ProcessedExtensionConfig {
  logger.info('开始处理插件配置...');

  try {
    // 第4步：校验 - 检查 name 和 version 是否已在顶层定义
    validateUserConfig(userConfig);

    // 第1步：遍历 userConfig.variants 数组
    const processedVariants: ProcessedVariantConfig[] = [];
    const variantTargets = new Set<string>();

    for (const variant of userConfig.variants) {
      // 第2步：使用 lodash-es/merge 合并全局配置与当前 variant 配置
      // 第3步：规范化与自动填充
      const processedVariant = processVariantConfig(userConfig, variant);

      // 第4步：确保生成的 variantTarget 是唯一的，防止构建冲突
      checkUniqueVariantTargets(variantTargets, processedVariant.variantTarget);
      variantTargets.add(processedVariant.variantTarget);

      processedVariants.push(processedVariant);
    }

    const result: ProcessedExtensionConfig = {
      name: userConfig.name,
      version: userConfig.version,
      variants: processedVariants,
    };

    logger.info(`配置处理完成，共处理 ${processedVariants.length} 个变体`);
    logger.verbose('处理结果:', result);

    return result;
  } catch (error) {
    logger.error('配置处理失败:', error);
    throw error;
  }
}

// 导出类型定义，方便用户使用
export type {
  UserExtensionConfig,
  VariantConfig,
  ProcessedExtensionConfig,
  ProcessedVariantConfig,
  VariantType,
  WebstoreType,
  ManifestVersion,
  DefaultValues,
  // 第四阶段新增的类型
  ListExtensionVariantsOptions,
  LightweightVariantInfo,
  GenerateExtensionConfigsOptions,
  FullVariantConfig,
} from './types.js';

// 导出常量，方便其他模块使用
export {
  DEFAULT_VALUES,
  WEBSTORE_TO_CN_MAPPING,
  SUPPORTED_WEBSTORES,
  SUPPORTED_VARIANT_TYPES,
  SUPPORTED_MANIFEST_VERSIONS,
} from './constants';

// #region --- 第四阶段：上层功能接口 ---

/**
 * 列出所有或指定的插件及其渠道包信息
 *
 * 功能说明：
 * - extensionNames 为空时，扫描 packages/extensions 目录，查找所有插件
 * - 动态加载每个插件的 extension.config.ts
 * - 返回处理后的配置，但会过滤掉 i18n 和 manifest 字段，使其轻量化，适合展示和选择
 *
 * @param options - 查询选项
 * @returns 轻量化的变体信息数组
 */
export async function listExtensionVariants(
  options: ListExtensionVariantsOptions = {},
): Promise<LightweightVariantInfo[]> {
  logger.info('开始列出插件变体信息...');

  try {
    const { extensionNames } = options;
    let targetExtensions: string[];

    if (extensionNames && extensionNames.length > 0) {
      // 使用指定的插件名称
      targetExtensions = extensionNames;
      logger.info(`使用指定的插件列表: ${targetExtensions.join(', ')}`);
    } else {
      // 扫描 packages/extensions 目录
      const extensionsDir = path.resolve(process.cwd(), 'packages/extensions');
      const entries = await fs.readdir(extensionsDir, { withFileTypes: true });
      targetExtensions = entries.filter((entry) => entry.isDirectory()).map((entry) => entry.name);
      logger.info(`扫描到 ${targetExtensions.length} 个插件: ${targetExtensions.join(', ')}`);
    }

    const allVariants: LightweightVariantInfo[] = [];

    for (const extensionName of targetExtensions) {
      try {
        // 动态加载插件的 extension.config.ts
        const configPath = path.resolve(
          process.cwd(),
          'packages/extensions',
          extensionName,
          'extension.config.ts',
        );

        // 检查配置文件是否存在
        try {
          await fs.access(configPath);
        } catch {
          logger.warn(`插件 ${extensionName} 的配置文件不存在，跳过: ${configPath}`);
          continue;
        }

        // 动态导入配置
        const configModule = await import(configPath);
        const config: ProcessedExtensionConfig = configModule.default;

        // 转换为轻量化的变体信息
        const lightweightVariants: LightweightVariantInfo[] = config.variants.map((variant) => ({
          name: variant.name,
          version: variant.version,
          variantId: variant.variantId,
          variantName: variant.variantName,
          variantType: variant.variantType,
          variantChannel: variant.variantChannel,
          variantTarget: variant.variantTarget,
          webstore: variant.webstore,
          webstoreCN: variant.webstoreCN,
          webstoreId: variant.webstoreId,
          webstoreUrl: variant.webstoreUrl,
          manifestVersion: variant.manifestVersion,
          defaultLocale: variant.defaultLocale,
          measurementId: variant.measurementId,
        }));

        allVariants.push(...lightweightVariants);
        logger.verbose(`插件 ${extensionName} 包含 ${lightweightVariants.length} 个变体`);
      } catch (error) {
        logger.error(`处理插件 ${extensionName} 时出错:`, error);
        // 继续处理其他插件，不中断整个流程
      }
    }

    logger.info(`成功列出 ${allVariants.length} 个变体信息`);
    return allVariants;
  } catch (error) {
    logger.error('列出插件变体信息失败:', error);
    throw error;
  }
}

/**
 * 为指定的插件和一个或多个渠道包生成完整的、可用于构建的配置数据
 *
 * 功能说明：
 * - variantNames 为空时，处理该插件下的所有渠道包
 * - 调用核心处理器 (defineExtensionConfig) 获取基础配置
 * - 编排：调用 scripts/i18n 和 scripts/manifest 的辅助函数，填充 i18n 和 manifest 的具体内容
 * - 返回一个包含所有完整信息的对象数组，每个对象对应一个渠道包
 *
 * @param options - 配置生成选项
 * @returns 完整的变体配置数组
 */
export async function generateExtensionConfigs(
  options: GenerateExtensionConfigsOptions,
): Promise<FullVariantConfig[]> {
  logger.info(`开始为插件 ${options.extensionName} 生成完整配置...`);

  try {
    const { extensionName, variantNames } = options;

    // 1. 动态加载插件的 extension.config.ts
    const configPath = path.resolve(
      process.cwd(),
      'packages/extensions',
      extensionName,
      'extension.config.ts',
    );

    // 检查配置文件是否存在
    try {
      await fs.access(configPath);
    } catch {
      throw new Error(`插件 ${extensionName} 的配置文件不存在: ${configPath}`);
    }

    // 动态导入配置
    const configModule = await import(configPath);
    const config: ProcessedExtensionConfig = configModule.default;

    // 2. 筛选需要处理的变体
    let targetVariants = config.variants;
    if (variantNames && variantNames.length > 0) {
      targetVariants = config.variants.filter(
        (variant) =>
          variantNames.includes(variant.variantId) || variantNames.includes(variant.variantName),
      );

      if (targetVariants.length === 0) {
        throw new Error(`在插件 ${extensionName} 中未找到指定的变体: ${variantNames.join(', ')}`);
      }

      logger.info(`筛选出 ${targetVariants.length} 个指定变体`);
    } else {
      logger.info(`处理插件 ${extensionName} 的所有 ${targetVariants.length} 个变体`);
    }

    // 3. 为每个变体生成完整配置
    const fullConfigs: FullVariantConfig[] = [];

    for (const variant of targetVariants) {
      try {
        logger.verbose(`处理变体: ${variant.variantTarget}`);

        // TODO: 调用 scripts/i18n 模块处理国际化
        // const i18nResult = await processLocalesForVariant({
        //   extensionName: variant.name,
        //   variantTarget: variant.variantTarget,
        //   i18nConfig: variant.i18n
        // });

        // TODO: 调用 scripts/manifest 模块处理清单文件
        // const manifestResult = await generateManifestForVariant({
        //   baseManifest: variant.manifest,
        //   variantInfo: {
        //     version: variant.version,
        //     manifestVersion: variant.manifestVersion,
        //     defaultLocale: variant.defaultLocale
        //   }
        // });

        // 临时实现：创建基础的完整配置结构
        const fullConfig: FullVariantConfig = {
          ...variant,
          i18n: {
            ...variant.i18n,
            // 临时的空实现，等待 i18n 模块完成
            chromeMessages: {},
            vueI18nMessages: {},
          },
          manifest: {
            ...variant.manifest,
            // 确保必要字段存在
            name: '__MSG_EXTENSION_NAME__',
            description: '__MSG_EXTENSION_DESCRIPTION__',
          },
        };

        fullConfigs.push(fullConfig);
        logger.verbose(`变体 ${variant.variantTarget} 配置生成完成`);
      } catch (error) {
        logger.error(`处理变体 ${variant.variantTarget} 时出错:`, error);
        throw error;
      }
    }

    logger.info(`成功为插件 ${extensionName} 生成 ${fullConfigs.length} 个完整配置`);
    return fullConfigs;
  } catch (error) {
    logger.error(`为插件 ${options.extensionName} 生成配置失败:`, error);
    throw error;
  }
}

/**
 * 将 generateExtensionConfigs 生成的数据写入到文件系统
 *
 * 功能说明：
 * - 输入: generateExtensionConfigs 的返回结果
 * - 输出:
 *   - .variants/{variantTarget}/extension.json: 过滤掉 i18n 和 manifest 的核心配置
 *   - .variants/{variantTarget}/i18n.json: 供 Vue 等前端框架使用的语言包
 *   - public/_locales/{locale}/messages.json: 供浏览器扩展 API 使用的 messages.json 文件
 *
 * @param configs - 完整的变体配置数组
 * @returns 写入的文件路径列表
 */
export async function writeExtensionConfigs(configs: FullVariantConfig[]): Promise<string[]> {
  logger.info(`开始写入 ${configs.length} 个变体配置到文件系统...`);

  if (configs.length === 0) {
    logger.warn('没有配置需要写入');
    return [];
  }

  try {
    const writtenFiles: string[] = [];

    for (const config of configs) {
      const { name: extensionName, variantTarget } = config;
      const variantDir = resolve(
        process.cwd(),
        'packages/extensions',
        extensionName,
        '.variants',
        variantTarget,
      );

      logger.verbose(`写入变体 ${variantTarget} 的配置文件...`);

      // 确保目录存在
      await fs.mkdir(variantDir, { recursive: true });

      // 1. 写入 extension.json - 过滤掉 i18n 和 manifest 的核心配置
      const coreConfig = {
        name: config.name,
        version: config.version,
        manifestVersion: config.manifestVersion,
        defaultLocale: config.defaultLocale,
        measurementId: config.measurementId,
        variantId: config.variantId,
        variantName: config.variantName,
        variantType: config.variantType,
        variantChannel: config.variantChannel,
        variantTarget: config.variantTarget,
        webstore: config.webstore,
        webstoreCN: config.webstoreCN,
        webstoreId: config.webstoreId,
        webstoreUrl: config.webstoreUrl,
        // 只保留基础的 i18n 配置，不包含具体的消息内容
        i18n: {
          locales: config.i18n.locales,
          includes: config.i18n.includes,
          excludes: config.i18n.excludes,
          chromeLocalesOnly: config.i18n.chromeLocalesOnly,
          chromeMessagesOnly: config.i18n.chromeMessagesOnly,
        },
      };

      const extensionJsonPath = join(variantDir, 'extension.json');
      await fs.writeFile(extensionJsonPath, JSON.stringify(coreConfig, null, 2), 'utf-8');
      writtenFiles.push(extensionJsonPath);

      // 2. 写入 i18n.json - 供 Vue 等前端框架使用的语言包
      const i18nJsonPath = join(variantDir, 'i18n.json');
      await fs.writeFile(
        i18nJsonPath,
        JSON.stringify(config.i18n.vueI18nMessages, null, 2),
        'utf-8',
      );
      writtenFiles.push(i18nJsonPath);

      // 3. 写入 public/_locales/{locale}/messages.json - 供浏览器扩展 API 使用
      const publicDir = join(variantDir, 'public', '_locales');
      await fs.mkdir(publicDir, { recursive: true });

      for (const [locale, messages] of Object.entries(config.i18n.chromeMessages)) {
        const localeDir = join(publicDir, locale);
        await fs.mkdir(localeDir, { recursive: true });

        const messagesJsonPath = join(localeDir, 'messages.json');
        await fs.writeFile(messagesJsonPath, JSON.stringify(messages, null, 2), 'utf-8');
        writtenFiles.push(messagesJsonPath);
      }

      logger.verbose(`变体 ${variantTarget} 的配置文件写入完成`);
    }

    logger.info(`成功写入 ${writtenFiles.length} 个配置文件`);
    return writtenFiles;
  } catch (error) {
    logger.error('写入配置文件失败:', error);
    throw error;
  }
}

// #endregion
