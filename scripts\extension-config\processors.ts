/**
 * @fileoverview 配置处理器模块
 * @description 提供配置合并、处理和规范化的核心逻辑
 */

import { merge } from 'lodash-es';
import type { 
  UserExtensionConfig, 
  VariantConfig, 
  ProcessedVariantConfig,
  ManifestVersion 
} from './types.js';
import { DEFAULT_VALUES } from './constants.js';
import { 
  generateVariantTarget, 
  generateVariantChannel, 
  generateWebstoreCN, 
  normalizeField,
  isEmptyString 
} from './utils.js';

/**
 * 处理单个变体配置
 * 执行配置合并、规范化和自动填充
 * 
 * @param globalConfig - 全局配置
 * @param variantConfig - 变体配置
 * @returns 处理后的变体配置
 */
export function processVariantConfig(
  globalConfig: UserExtensionConfig,
  variantConfig: VariantConfig,
): ProcessedVariantConfig {
  // 第一步：使用 lodash-es/merge 合并全局配置和变体配置
  const mergedConfig = mergeConfigurations(globalConfig, variantConfig);
  
  // 第二步：规范化和自动填充字段
  const normalizedConfig = normalizeVariantFields(mergedConfig, globalConfig);
  
  return normalizedConfig;
}

/**
 * 合并全局配置和变体配置
 * 使用 lodash-es 的深度合并功能
 * 
 * @param globalConfig - 全局配置
 * @param variantConfig - 变体配置
 * @returns 合并后的配置
 */
export function mergeConfigurations(
  globalConfig: UserExtensionConfig,
  variantConfig: VariantConfig,
): VariantConfig & { name: string; version: string } {
  // 创建基础配置对象，只包含可以被继承的字段
  const baseConfig = {
    name: globalConfig.name,
    version: globalConfig.version,
    manifestVersion: globalConfig.manifestVersion,
    defaultLocale: globalConfig.defaultLocale,
    measurementId: globalConfig.measurementId,
    i18n: globalConfig.i18n,
    manifest: globalConfig.manifest,
  };

  // 使用 lodash-es 深度合并
  return merge(baseConfig, variantConfig);
}

/**
 * 规范化变体字段
 * 自动填充缺失的字段并确保数据完整性
 * 
 * @param mergedConfig - 合并后的配置
 * @param globalConfig - 原始全局配置
 * @returns 规范化后的变体配置
 */
export function normalizeVariantFields(
  mergedConfig: VariantConfig & { name: string; version: string },
  globalConfig: UserExtensionConfig,
): ProcessedVariantConfig {
  // 确定最终的 manifestVersion 和 defaultLocale
  const manifestVersion: ManifestVersion = normalizeField(
    mergedConfig.manifestVersion, 
    DEFAULT_VALUES.manifestVersion
  );
  
  const defaultLocale: string = normalizeField(
    mergedConfig.defaultLocale, 
    DEFAULT_VALUES.defaultLocale
  );

  // 自动生成 variantTarget
  const variantTarget = generateVariantTarget(
    mergedConfig.webstore,
    manifestVersion,
    mergedConfig.variantType
  );

  // 自动填充 variantChannel（如果为空）
  const variantChannel = isEmptyString(mergedConfig.variantChannel)
    ? generateVariantChannel(mergedConfig.webstore, mergedConfig.variantType)
    : mergedConfig.variantChannel!;

  // 自动填充 webstoreCN（如果为空）
  const webstoreCN = isEmptyString(mergedConfig.webstoreCN)
    ? generateWebstoreCN(mergedConfig.webstore)
    : mergedConfig.webstoreCN!;

  // 处理 i18n 配置
  const i18nConfig = fillDefaultI18nValues(mergedConfig.i18n);

  // 处理 manifest 配置
  const manifestConfig = fillDefaultManifestValues(
    mergedConfig.manifest,
    globalConfig.version,
    manifestVersion,
    defaultLocale
  );

  return {
    // 元数据
    name: globalConfig.name,
    version: globalConfig.version,

    // 变体核心字段
    variantId: mergedConfig.variantId,
    variantName: mergedConfig.variantName,
    variantType: mergedConfig.variantType,
    variantChannel,
    variantTarget,
    webstore: mergedConfig.webstore,
    webstoreCN,
    webstoreId: mergedConfig.webstoreId,
    webstoreUrl: mergedConfig.webstoreUrl,

    // 插件核心配置
    manifestVersion,
    defaultLocale,
    measurementId: mergedConfig.measurementId,

    // i18n 配置
    i18n: i18nConfig,

    // manifest 配置
    manifest: manifestConfig,
  };
}

/**
 * 填充默认的 i18n 配置值
 * 
 * @param i18nConfig - 用户提供的 i18n 配置
 * @returns 填充默认值后的 i18n 配置
 */
function fillDefaultI18nValues(i18nConfig?: UserExtensionConfig['i18n']) {
  return {
    locales: [], // 将由 i18n 模块在文件系统扫描时填充
    includes: i18nConfig?.includes || [],
    excludes: i18nConfig?.excludes || [],
    chromeLocalesOnly: i18nConfig?.chromeLocalesOnly || DEFAULT_VALUES.chromeLocalesOnly,
    chromeMessagesOnly: i18nConfig?.chromeMessagesOnly || DEFAULT_VALUES.chromeMessagesOnly,
  };
}

/**
 * 填充默认的 manifest 配置值
 * 强制写入关键字段以确保一致性
 * 
 * @param manifestConfig - 用户提供的 manifest 配置
 * @param version - 插件版本
 * @param manifestVersion - Manifest 版本
 * @param defaultLocale - 默认语言
 * @returns 填充默认值后的 manifest 配置
 */
function fillDefaultManifestValues(
  manifestConfig: Record<string, any> | undefined,
  version: string,
  manifestVersion: ManifestVersion,
  defaultLocale: string,
) {
  return {
    ...manifestConfig,
    // 强制写入这些字段以确保与顶层配置一致
    version,
    manifest_version: manifestVersion,
    default_locale: defaultLocale,
  };
}
