/**
 * @fileoverview Extension configuration module test
 * @description Basic test to verify the extension-config module works according to documentation
 */

import { defineExtensionConfig } from './index.js';
import type { UserExtensionConfig } from './types.js';

// Test configuration based on the existing cookies_manager example
const testConfig: UserExtensionConfig = {
  name: 'test_extension',
  version: '1.0.0',
  manifestVersion: 3,
  defaultLocale: 'en',
  measurementId: 'G-TEST123',
  i18n: {
    // Test the warning for user-provided locales (should be auto-generated)
    locales: ['en', 'zh_CN'], // This should trigger a warning
    chromeLocalesOnly: ['en_US', 'en_GB'],
    chromeMessagesOnly: ['EXTENSION_NAME', 'EXTENSION_DESCRIPTION'],
  },
  manifest: {
    name: '__MSG_EXTENSION_NAME__',
    description: '__MSG_EXTENSION_DESCRIPTION__',
    permissions: ['storage', 'activeTab'],
  },
  variants: [
    {
      variantId: '1001',
      variantName: 'Master Channel',
      variantType: 'master',
      webstore: 'chrome',
      webstoreId: 'test-chrome-id',
      measurementId: 'G-CHROME123',
    },
    {
      variantId: '1002',
      variantName: 'Firefox Channel',
      variantType: 'master',
      webstore: 'firefox',
      manifestVersion: 2, // Override global manifest version
      defaultLocale: 'zh_CN', // Override global default locale
    },
    {
      variantId: '1003',
      variantName: 'Offline Channel',
      variantType: 'offline',
      webstore: 'chrome',
      variantChannel: 'custom_offline', // Manual override
      webstoreCN: 'custom-cn', // Manual override
    },
  ],
};

/**
 * Test the defineExtensionConfig function
 */
export function testExtensionConfig(): void {
  console.log('Testing defineExtensionConfig...');
  
  try {
    // Test the main function
    const result = defineExtensionConfig(testConfig);
    
    // Verify the result structure
    console.assert(result.name === 'test_extension', 'Name should be preserved');
    console.assert(result.version === '1.0.0', 'Version should be preserved');
    console.assert(result.variants.length === 3, 'Should have 3 variants');
    
    // Test variant processing
    const [masterChrome, masterFirefox, offlineChrome] = result.variants;
    
    // Test Chrome Master variant
    console.assert(masterChrome.variantTarget === 'chrome-mv3-master', 'Chrome master variant target should be correct');
    console.assert(masterChrome.variantChannel === 'chrome', 'Chrome master variant channel should be auto-filled');
    console.assert(masterChrome.webstoreCN === 'e-c', 'Chrome webstoreCN should be auto-filled');
    console.assert(masterChrome.manifestVersion === 3, 'Chrome master should inherit global manifest version');
    console.assert(masterChrome.defaultLocale === 'en', 'Chrome master should inherit global default locale');
    console.assert(masterChrome.measurementId === 'G-CHROME123', 'Chrome master should use variant-specific measurementId');
    console.assert(masterChrome.manifest.manifest_version === 3, 'Manifest should have manifest_version written internally');
    console.assert(masterChrome.manifest.default_locale === 'en', 'Manifest should have default_locale written internally');
    
    // Test Firefox Master variant
    console.assert(masterFirefox.variantTarget === 'firefox-mv2-master', 'Firefox master variant target should be correct');
    console.assert(masterFirefox.variantChannel === 'firefox', 'Firefox master variant channel should be auto-filled');
    console.assert(masterFirefox.webstoreCN === 'e-f', 'Firefox webstoreCN should be auto-filled');
    console.assert(masterFirefox.manifestVersion === 2, 'Firefox master should use variant-specific manifest version');
    console.assert(masterFirefox.defaultLocale === 'zh_CN', 'Firefox master should use variant-specific default locale');
    console.assert(masterFirefox.measurementId === 'G-TEST123', 'Firefox master should inherit global measurementId');
    console.assert(masterFirefox.manifest.manifest_version === 2, 'Firefox manifest should have manifest_version written internally');
    console.assert(masterFirefox.manifest.default_locale === 'zh_CN', 'Firefox manifest should have default_locale written internally');
    
    // Test Offline Chrome variant
    console.assert(offlineChrome.variantTarget === 'chrome-mv3-offline', 'Chrome offline variant target should be correct');
    console.assert(offlineChrome.variantChannel === 'custom_offline', 'Chrome offline should use manual variantChannel override');
    console.assert(offlineChrome.webstoreCN === 'custom-cn', 'Chrome offline should use manual webstoreCN override');
    
    // Test i18n configuration
    console.assert(masterChrome.i18n.locales.length === 0, 'i18n.locales should be empty (to be filled by i18n module)');
    console.assert(masterChrome.i18n.chromeLocalesOnly.length === 2, 'Should inherit chromeLocalesOnly from user config');
    console.assert(masterChrome.i18n.chromeMessagesOnly.length === 2, 'Should inherit chromeMessagesOnly from user config');
    
    // Test data redundancy (each variant should be self-contained)
    console.assert(masterChrome.name === 'test_extension', 'Each variant should contain name');
    console.assert(masterChrome.version === '1.0.0', 'Each variant should contain version');
    console.assert(masterFirefox.name === 'test_extension', 'Each variant should contain name');
    console.assert(masterFirefox.version === '1.0.0', 'Each variant should contain version');
    
    console.log('✅ All tests passed!');
    return;
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  }
}

/**
 * Test validation errors
 */
export function testValidationErrors(): void {
  console.log('Testing validation errors...');
  
  // Test missing required fields
  try {
    defineExtensionConfig({} as any);
    console.error('❌ Should have thrown error for missing required fields');
  } catch (error) {
    console.log('✅ Correctly caught missing required fields error');
  }
  
  // Test invalid variant type
  try {
    defineExtensionConfig({
      name: 'test',
      version: '1.0.0',
      variants: [{
        variantId: '1',
        variantName: 'Test',
        variantType: 'invalid' as any,
        webstore: 'chrome',
      }],
    });
    console.error('❌ Should have thrown error for invalid variant type');
  } catch (error) {
    console.log('✅ Correctly caught invalid variant type error');
  }
  
  // Test duplicate variant targets
  try {
    defineExtensionConfig({
      name: 'test',
      version: '1.0.0',
      variants: [
        {
          variantId: '1',
          variantName: 'Test 1',
          variantType: 'master',
          webstore: 'chrome',
        },
        {
          variantId: '2',
          variantName: 'Test 2',
          variantType: 'master',
          webstore: 'chrome', // Same variantTarget will be generated
        },
      ],
    });
    console.error('❌ Should have thrown error for duplicate variant targets');
  } catch (error) {
    console.log('✅ Correctly caught duplicate variant targets error');
  }
  
  console.log('✅ All validation tests passed!');
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  testExtensionConfig();
  testValidationErrors();
  console.log('🎉 All extension-config tests completed successfully!');
}