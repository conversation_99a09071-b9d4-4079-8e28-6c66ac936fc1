/**
 * @fileoverview 配置管理模块类型定义
 * @description 定义用户配置和处理后配置的 TypeScript 接口
 */

// #region --- 基础类型定义 ---

/**
 * 支持的变体类型
 */
export type VariantType = 'master' | 'tm' | 'tmBeta' | 'dba' | 'offline';

/**
 * 支持的浏览器商店
 */
export type WebstoreType =
  | 'chrome'
  | 'firefox'
  | 'edge'
  | 'opera'
  | 'browser360'
  | 'safari'
  | 'adspower';

/**
 * 支持的 Manifest 版本
 */
export type ManifestVersion = 2 | 3;

// #endregion

// #region --- 用户配置接口 ---

/**
 * 用户在 extension.config.ts 中定义的配置结构
 */
export interface UserExtensionConfig {
  /**
   * [必填] 插件项目名称，与 `packages/extensions/` 下的文件夹名一致
   */
  name: string;

  /**
   * [必填] 插件版本号，遵循 SemVer 规范
   */
  version: string;

  /**
   * [可选] 默认的 manifest 版本号。可被 variant 覆盖
   */
  manifestVersion?: ManifestVersion;

  /**
   * [可选] 插件的默认语言环境。可被 variant 覆盖
   */
  defaultLocale?: string;

  /**
   * [可选] Google Analytics Measurement ID。可被 variant 覆盖
   */
  measurementId?: string;

  /**
   * [可选] 国际化配置
   */
  i18n?: {
    /**
     * [只读] 插件支持的语言列表。由脚本自动扫描填充，用户无需填写
     */
    readonly locales?: string[];

    /**
     * [可选] 包含的文案 key (支持正则字符串)
     */
    includes?: string[];

    /**
     * [可选] 排除的文案 key (支持正则字符串)
     */
    excludes?: string[];

    /**
     * [可选] 仅生成到 `_locales` 目录的语言
     * 默认值: ['en_US', 'en_GB', 'pt_BR', 'es_419']
     */
    chromeLocalesOnly?: string[];

    /**
     * [可选] 仅包含在 `_locales` 目录中的文案 key (支持正则字符串)
     * 默认值: ['EXTENSION_NAME', 'EXTENSION_DESCRIPTION', '^context_menu_.*']
     */
    chromeMessagesOnly?: string[];
  };

  /**
   * [可选] 标准的 manifest.json 配置骨架。所有 variant 都会继承此配置
   * 注意: version, manifest_version, default_locale, name, description 字段会被脚本自动处理
   */
  manifest?: Record<string, any>;

  /**
   * [必填] 渠道包 (变体) 定义数组
   */
  variants: VariantConfig[];
}

/**
 * 单个渠道包 (变体) 配置定义
 */
export interface VariantConfig extends Omit<UserExtensionConfig, 'name' | 'version' | 'variants'> {
  /**
   * [必填] 渠道包的唯一 ID
   */
  variantId: string;

  /**
   * [必填] 渠道包的用户友好名称
   */
  variantName: string;

  /**
   * [必填] 渠道包类型
   */
  variantType: VariantType;

  /**
   * [必填] 目标浏览器商店的简称
   */
  webstore: WebstoreType;

  /**
   * [可选] 渠道代码名称。如果留空，脚本将自动填充
   * 自动填充规则: 如果 `variantType` 是 'offline', 格式为 `{webstore}_offline`，否则为 `{webstore}`
   */
  variantChannel?: string;

  /**
   * [可选] 目标浏览器的内部代码名称。如果留空，脚本将自动填充
   */
  webstoreCN?: string;

  /**
   * [可选] 插件在对应商店的 ID
   */
  webstoreId?: string;

  /**
   * [可选] 插件在对应商店的 URL
   */
  webstoreUrl?: string;

  /**
   * [只读] 渠道包的最终构建目标标识符。由脚本自动生成
   * 格式: `{webstore}-mv{manifestVersion}-{variantType}`
   */
  readonly variantTarget?: string;
}

// #endregion

// #region --- 处理后配置接口 ---

/**
 * 经过 defineExtensionConfig 处理后的配置对象结构
 */
export interface ProcessedExtensionConfig {
  /**
   * 插件项目名称，直接从用户配置中获取
   */
  name: string;

  /**
   * 插件版本号，直接从用户配置中获取
   */
  version: string;

  /**
   * 处理过的渠道包 (变体) 对象数组
   * 每个对象都是一个独立的、完整的配置单元
   */
  variants: ProcessedVariantConfig[];
}

/**
 * 单个渠道包 (变体) 的完整配置结构
 * 继承了全局配置，并应用了自己的特定配置
 * 所有可选字段都已被填充或赋予了确定的默认值
 */
export interface ProcessedVariantConfig {
  // --- 从全局继承或自身定义的元数据 ---
  name: string;
  version: string;

  // --- 被规范化和填充的 Variant 核心字段 ---
  variantId: string;
  variantName: string;
  variantType: VariantType;
  variantChannel: string; // [已填充]
  variantTarget: string; // [已填充]
  webstore: WebstoreType;
  webstoreCN: string; // [已填充]
  webstoreId?: string;
  webstoreUrl?: string;

  // --- 被规范化和填充的插件核心配置 ---
  manifestVersion: ManifestVersion; // [已确定]
  defaultLocale: string; // [已确定]
  measurementId?: string;

  // --- 完整的 i18n 配置 ---
  i18n: {
    locales: string[]; // [已扫描填充]
    includes?: string[];
    excludes?: string[];
    chromeLocalesOnly: string[]; // [已赋予默认值]
    chromeMessagesOnly: string[]; // [已赋予默认值]
  };

  // --- 完整的、合并后的 manifest 对象 ---
  manifest: {
    // [已强制写入] 这些字段的值与顶层的 manifestVersion 和 defaultLocale 一致
    version: string;
    manifest_version: ManifestVersion;
    default_locale: string;
  } & Record<string, any>;
}

// #endregion

// #region --- 上层功能接口类型 ---

/**
 * 查询接口选项
 */
export interface ListExtensionVariantsOptions {
  /**
   * [可选] 指定要查询的插件名称列表
   * 如果为空，则扫描 packages/extensions 目录下的所有插件
   */
  extensionNames?: string[];
}

/**
 * 轻量化的变体信息，用于展示和选择
 * 过滤掉 i18n 和 manifest 字段，使其轻量化
 */
export interface LightweightVariantInfo {
  // --- 插件基本信息 ---
  name: string;
  version: string;

  // --- 变体核心信息 ---
  variantId: string;
  variantName: string;
  variantType: VariantType;
  variantChannel: string;
  variantTarget: string;
  webstore: WebstoreType;
  webstoreCN: string;
  webstoreId?: string;
  webstoreUrl?: string;

  // --- 插件核心配置 ---
  manifestVersion: ManifestVersion;
  defaultLocale: string;
  measurementId?: string;
}

/**
 * 配置生成接口选项
 */
export interface GenerateExtensionConfigsOptions {
  /**
   * [必填] 指定的插件名称
   */
  extensionName: string;

  /**
   * [可选] 指定要生成配置的变体名称列表
   * 如果为空，则处理该插件下的所有变体
   */
  variantNames?: string[];
}

/**
 * 完整的变体配置，包含 i18n 和 manifest 的具体内容
 * 这是 generateExtensionConfigs 的返回类型
 */
export interface FullVariantConfig extends ProcessedVariantConfig {
  /**
   * 完整的 i18n 数据，由 scripts/i18n 模块填充
   */
  i18n: {
    locales: string[];
    includes?: string[];
    excludes?: string[];
    chromeLocalesOnly: string[];
    chromeMessagesOnly: string[];

    // 由 i18n 模块生成的具体内容
    chromeMessages: Record<string, Record<string, any>>;
    vueI18nMessages: Record<string, Record<string, any>>;
  };

  /**
   * 完整的 manifest 对象，由 scripts/manifest 模块填充
   */
  manifest: {
    version: string;
    manifest_version: ManifestVersion;
    default_locale: string;
    name: string;
    description: string;
  } & Record<string, any>;
}

// #endregion

// #region --- I18n 和 Manifest 模块接口类型 ---

/**
 * Chrome 消息条目结构
 */
export interface ChromeMessageEntry {
  /**
   * 消息内容
   */
  message: string;

  /**
   * 消息描述
   */
  description?: string;

  /**
   * 占位符定义
   */
  placeholders?: Record<
    string,
    {
      content: string;
      example?: string;
    }
  >;

  /**
   * 条件化消息：支持根据 variantTarget 覆盖消息内容
   * 例如: { "chrome-mv3-master": "特定版本的消息" }
   */
  [variantTarget: string]: any;
}

/**
 * 语言包数据结构
 */
export interface LocaleData {
  [locale: string]: Record<string, ChromeMessageEntry>;
}

/**
 * I18n 模块输入接口
 */
export interface I18nInput {
  /**
   * 插件名称
   */
  extensionName: string;

  /**
   * 变体目标标识符
   */
  variantTarget: string;

  /**
   * I18n 配置
   */
  i18nConfig: {
    includes?: string[];
    excludes?: string[];
    chromeLocalesOnly: string[];
    chromeMessagesOnly: string[];
  };
}

/**
 * I18n 模块输出接口
 */
export interface I18nOutput {
  /**
   * Chrome 扩展格式的消息
   */
  chromeMessages: Record<string, Record<string, any>>;

  /**
   * Vue i18n 格式的消息
   */
  vueI18nMessages: Record<string, Record<string, any>>;
}

/**
 * Manifest 模块输入接口
 */
export interface ManifestInput {
  /**
   * 基础 manifest 配置
   */
  baseManifest: Record<string, any>;

  /**
   * 变体信息
   */
  variantInfo: {
    name: string;
    version: string;
    manifestVersion: ManifestVersion;
    defaultLocale: string;
    variantId: string;
    variantName: string;
    variantType: string;
    webstore: string;
    measurementId?: string;
  };
}

/**
 * 最终的 Manifest 对象
 */
export interface FinalManifest {
  manifest_version: ManifestVersion;
  name: string;
  description: string;
  version: string;
  default_locale: string;
  [key: string]: any;
}

// #endregion

// #region --- 工具类型 ---

/**
 * 默认值配置
 */
export interface DefaultValues {
  manifestVersion: ManifestVersion;
  defaultLocale: string;
  chromeLocalesOnly: string[];
  chromeMessagesOnly: string[];
}

// #endregion
