/**
 * @fileoverview Extension configuration system TypeScript type definitions
 * @description Defines user configuration, processed configuration, and various interface type structures
 */

import type { <PERSON><PERSON><PERSON> } from 'wxt/browser';

// #region --- User Configuration Interface ---

/**
 * User configuration structure defined in extension.config.ts
 */
export interface UserExtensionConfig {
  /**
   * [Required] Plugin project name, consistent with folder name under `packages/extensions/`
   */
  name: string;

  /**
   * [Required] Plugin version number, following SemVer specification
   */
  version: string;

  /**
   * [Optional] Default manifest version number (e.g., 3). Can be overridden by variant
   */
  manifestVersion?: Browser.runtime.Manifest['manifest_version'];

  /**
   * [Optional] Plugin default locale (e.g., 'en'). Can be overridden by variant
   */
  defaultLocale?: string;

  /**
   * [Optional] Google Analytics Measurement ID. Can be overridden by variant
   */
  measurementId?: string;

  /**
   * [Optional] Internationalization (i18n) processing configuration
   */
  i18n?: {
    /**
     * [Readonly] List of languages supported by the plugin. Automatically filled by script scanning, users do not need to fill
     */
    readonly locales?: string[];

    /**
     * [Optional] Included message keys (supports regex strings). If defined, only matching messages will be retained
     */
    includes?: string[];

    /**
     * [Optional] Excluded message keys (supports regex strings)
     */
    excludes?: string[];

    /**
     * [Optional] Languages only generated to `_locales` directory. These languages' messages will not appear in vue-i18n output
     * Default: ['en_US', 'en_GB', 'pt_BR', 'es_419']
     */
    chromeLocalesOnly?: string[];

    /**
     * [Optional] Message keys only included in `_locales` directory (supports regex strings). Takes effect after `includes`/`excludes` filtering
     * Default: ['EXTENSION_NAME', 'EXTENSION_DESCRIPTION', '^context_menu_.*']
     */
    chromeMessagesOnly?: string[];
  };

  /**
   * [Optional] Standard manifest.json configuration skeleton. All variants will inherit this configuration
   * Note: version, manifest_version, default_locale, name, description fields will be automatically processed by script, user input is invalid
   */
  manifest?: Partial<Browser.runtime.Manifest>;

  /**
   * [Required] Channel package (variant) definition array
   */
  variants: VariantConfig[];
}

/**
 * Single channel package (variant) configuration definition
 */
export interface VariantConfig extends Omit<UserExtensionConfig, 'name' | 'version' | 'variants'> {
  /**
   * [Required] Unique ID of the channel package
   */
  variantId: string;

  /**
   * [Required] User-friendly name of the channel package
   */
  variantName: string;

  /**
   * [Required] Channel package type
   */
  variantType: 'master' | 'tm' | 'tmBeta' | 'dba' | 'offline';

  /**
   * [Required] Short name of target browser store
   */
  webstore: 'chrome' | 'firefox' | 'edge' | 'opera' | 'browser360' | 'safari' | 'adspower';

  /**
   * [Optional] Channel code name. If left empty, script will automatically fill
   * Auto-fill rule: If `variantType` is 'offline', format is `{webstore}_offline`, otherwise `{webstore}`
   */
  variantChannel?: string;

  /**
   * [Optional] Target browser internal code name. If left empty, script will automatically fill
   */
  webstoreCN?: string;

  /**
   * [Optional] Plugin ID in corresponding store
   */
  webstoreId?: string;

  /**
   * [Optional] Plugin URL in corresponding store
   */
  webstoreUrl?: string;

  /**
   * [Readonly] Final build target identifier of the channel package. Automatically generated by script, users do not need to fill
   * Format: `{webstore}-mv{manifestVersion}-{variantType}`
   */
  readonly variantTarget?: string;
}

// #endregion

// #region --- Processed Configuration Interface ---

/**
 * Configuration object structure after processing by defineExtensionConfig
 */
export interface ProcessedExtensionConfig {
  /**
   * Plugin project name, directly obtained from user configuration
   */
  name: string;

  /**
   * Plugin version number, directly obtained from user configuration
   */
  version: string;

  /**
   * Array of processed channel package (variant) objects
   * Each object is an independent, complete configuration unit
   */
  variants: ProcessedVariantConfig[];
}

/**
 * Complete configuration structure of a single channel package (variant)
 * It inherits global configuration and applies its own specific configuration
 * All optional fields have been filled or given definite default values
 */
export interface ProcessedVariantConfig {
  // --- Metadata inherited from global or self-defined ---
  name: string;
  version: string;

  // --- Normalized and filled Variant core fields ---
  variantId: string;
  variantName: string;
  variantType: 'master' | 'tm' | 'tmBeta' | 'dba' | 'offline';
  variantChannel: string; // [Filled]
  variantTarget: string; // [Filled]
  webstore: 'chrome' | 'firefox' | 'edge' | 'opera' | 'browser360' | 'safari' | 'adspower';
  webstoreCN: string; // [Filled]
  webstoreId?: string;
  webstoreUrl?: string;

  // --- Normalized and filled plugin core configuration ---
  manifestVersion: 2 | 3; // [Determined]
  defaultLocale: string; // [Determined]
  measurementId?: string;

  // --- Complete i18n configuration ---
  i18n: {
    locales: string[]; // [Scanned and filled]
    includes?: string[];
    excludes?: string[];
    chromeLocalesOnly: string[]; // [Given default values]
    chromeMessagesOnly: string[]; // [Given default values]
  };

  // --- Complete, merged manifest object ---
  manifest: {
    // [Force written] These field values are consistent with top-level manifestVersion and defaultLocale
    version: string;
    manifest_version: 2 | 3;
    default_locale: string;
  } & Omit<Partial<Browser.runtime.Manifest>, 'version' | 'manifest_version' | 'default_locale'>;
}

// #endregion

// #region --- I18n Related Interfaces ---

/**
 * Locale data structure - stores raw locale data
 */
export interface LocaleData {
  [locale: string]: {
    [key: string]: ChromeMessageEntry;
  };
}

/**
 * Chrome extension message entry structure
 */
export interface ChromeMessageEntry {
  message: string;
  description?: string;
  placeholders?: {
    [key: string]: {
      content: string;
      example?: string;
    };
  };
  // Support conditional messages: use variantTarget as key to override default message
  [variantTarget: string]: any;
}

/**
 * I18n processing function input parameters
 */
export interface I18nInput {
  extensionName: string;
  variantTarget: string;
  i18nConfig: ProcessedVariantConfig['i18n'];
}

/**
 * I18n processing function output result
 */
export interface I18nOutput {
  /**
   * Message format for Chrome extension _locales directory
   */
  chromeMessages: {
    [locale: string]: {
      [key: string]: {
        message: string;
        description?: string;
        placeholders?: {
          [key: string]: {
            content: string;
            example?: string;
          };
        };
      };
    };
  };

  /**
   * Message format for vue-i18n
   */
  vueI18nMessages: {
    [locale: string]: {
      [key: string]: string;
    };
  };
}

// #endregion

// #region --- Manifest Related Interfaces ---

/**
 * Manifest processing function input parameters
 */
export interface ManifestInput {
  baseManifest: Partial<Browser.runtime.Manifest>;
  variantInfo: {
    name: string;
    version: string;
    manifestVersion: 2 | 3;
    defaultLocale: string;
    variantId: string;
    variantName: string;
    variantType: string;
    webstore: string;
    measurementId?: string;
  };
}

/**
 * Final generated Manifest object
 */
export type FinalManifest = {
  manifest_version: 2 | 3;
  name: string;
  version: string;
  default_locale: string;
  description?: string;
} & Omit<
  Browser.runtime.Manifest,
  'manifest_version' | 'name' | 'version' | 'default_locale' | 'description'
>;

// #endregion

// #region --- Extension.json File Structure ---

/**
 * Complete structure of generated extension.json file
 */
export interface ExtensionJson {
  name: string;
  version: string;
  manifestVersion: 2 | 3;
  defaultLocale: string;
  measurementId?: string;
  variantId: string;
  variantName: string;
  variantType: string;
  variantChannel: string;
  variantTarget: string;
  webstore: string;
  webstoreCN: string;
  webstoreId?: string;
  webstoreUrl?: string;
  i18n: I18nOutput;
  manifest: FinalManifest;
}

// #endregion

// #region --- Utility Types ---

/**
 * Default values configuration
 */
export interface DefaultValues {
  manifestVersion: 2 | 3;
  defaultLocale: string;
  chromeLocalesOnly: string[];
  chromeMessagesOnly: string[];
}

/**
 * File path configuration
 */
export interface PathConfig {
  extensionsRoot: string;
  sharedLocalesRoot: string;
  outputRoot: string;
}

// #endregion
