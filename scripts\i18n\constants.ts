/**
 * @fileoverview I18n 模块的常量定义
 * @description 定义国际化处理相关的常量
 */

import { SUPPORTED_LOCALES } from '@wxt-dev/i18n/build';

export { SUPPORTED_LOCALES };

export const isSupportedLocale = (locale: string): boolean => {
  return SUPPORTED_LOCALES.has(locale);
};

/**
 * 默认的 Chrome 专用语言代码
 */
export const DEFAULT_CHROME_LOCALES_ONLY = [
  'en_US',
  'en_GB', 
  'pt_BR',
  'es_419'
] as const;

/**
 * 默认的 Chrome 专用消息键名模式
 */
export const DEFAULT_CHROME_MESSAGES_ONLY = [
  'EXTENSION_NAME',
  'EXTENSION_DESCRIPTION',
  '^context_menu_.*'
] as const;

/**
 * 占位符转换映射
 */
export const PLACEHOLDER_PATTERNS = {
  // Chrome 格式: $placeholder$ -> vue-i18n 格式: {placeholder}
  CHROME_TO_VUE: /\$([^$]+)\$/g,
  // vue-i18n 格式: {placeholder} -> Chrome 格式: $placeholder$
  VUE_TO_CHROME: /\{([^}]+)\}/g,
} as const;

/**
 * 支持的语言包文件扩展名
 */
export const SUPPORTED_LOCALE_EXTENSIONS = ['.json'] as const;

/**
 * 语言包文件命名模式
 */
export const LOCALE_FILE_PATTERNS = {
  // 标准的语言代码格式
  STANDARD: /^[a-z]{2}(_[A-Z]{2})?\.json$/,
  // 带有区域变体的格式
  VARIANT: /^[a-z]{2}_[A-Z]{2}(_[A-Z]+)?\.json$/,
} as const;

/**
 * 文案键名验证规则
 */
export const MESSAGE_KEY_PATTERNS = {
  // 有效的消息键名格式
  VALID_KEY: /^[A-Z][A-Z0-9_]*$/,
  // 上下文菜单相关的键名
  CONTEXT_MENU: /^context_menu_/,
  // 扩展基础信息键名
  EXTENSION_INFO: /^EXTENSION_(NAME|DESCRIPTION|VERSION)$/,
} as const;

/**
 * 文案处理相关常量
 */
export const MESSAGE_PROCESSING = {
  // 最大消息长度（Chrome 扩展限制）
  MAX_MESSAGE_LENGTH: 512,
  // 最大占位符数量
  MAX_PLACEHOLDERS: 9,
  // 占位符名称最大长度
  MAX_PLACEHOLDER_NAME_LENGTH: 30,
} as const;

/**
 * 错误消息
 */
export const I18N_ERROR_MESSAGES = {
  LOCALE_NOT_FOUND: (locale: string) => `Locale '${locale}' not found`,
  INVALID_LOCALE_FORMAT: (locale: string) => `Invalid locale format: ${locale}`,
  MISSING_MESSAGE_KEY: (key: string, locale: string) => `Missing message key '${key}' in locale '${locale}'`,
  INVALID_MESSAGE_KEY: (key: string) => `Invalid message key format: ${key}`,
  PLACEHOLDER_MISMATCH: (key: string) => `Placeholder mismatch in message key: ${key}`,
  MESSAGE_TOO_LONG: (key: string, length: number) => `Message too long for key '${key}': ${length} characters (max: ${MESSAGE_PROCESSING.MAX_MESSAGE_LENGTH})`,
  INVALID_JSON: (file: string) => `Invalid JSON in locale file: ${file}`,
  FILE_NOT_FOUND: (path: string) => `Locale file not found: ${path}`,
} as const;

/**
 * 成功消息
 */
export const I18N_SUCCESS_MESSAGES = {
  LOCALES_LOADED: (count: number) => `Successfully loaded ${count} locales`,
  MESSAGES_PROCESSED: (count: number) => `Successfully processed ${count} messages`,
  FILES_GENERATED: (count: number) => `Successfully generated ${count} locale files`,
  VALIDATION_PASSED: 'Locale validation passed',
} as const;
