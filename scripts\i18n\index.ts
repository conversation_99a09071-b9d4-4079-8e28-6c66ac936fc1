/**
 * @fileoverview I18n module main entry point
 * @description Provides internationalization processing functionality
 */

import { createLogger } from '../utils/logger.js';

import type {
  LocaleData,
  ChromeMessageEntry,
  I18nInput,
  I18nOutput,
} from '../extension-config/types.js';

const logger = createLogger('I18n');

/**
 * Load shared locale data (简化实现)
 */
export async function loadSharedLocales(): Promise<LocaleData> {
  logger.info('使用简化的 loadSharedLocales 实现');
  return {};
}

/**
 * Load extension-specific locale data (简化实现)
 */
export async function loadExtensionLocales(extensionName: string): Promise<LocaleData> {
  logger.info(`使用简化的 loadExtensionLocales 实现: ${extensionName}`);
  return {};
}

/**
 * Get available locales for a specific extension (简化实现)
 */
export async function getAvailableLocales(extensionName: string): Promise<string[]> {
  logger.info(`使用简化的 getAvailableLocales 实现: ${extensionName}`);
  return ['en'];
}

/**
 * Process locales for a specific variant
 * This is the main function that generates the final i18n output
 */
export async function processLocalesForVariant(input: I18nInput): Promise<I18nOutput> {
  logger.info(`Processing locales for variant: ${input.variantTarget}`);

  try {
    // 简化实现：直接返回基础结构
    // 在实际项目中，这里会有完整的语言包处理逻辑
    logger.warn('使用简化的 i18n 实现，返回基础结构');

    return {
      chromeMessages: {
        en: {
          EXTENSION_NAME: { message: input.extensionName },
          EXTENSION_DESCRIPTION: { message: `${input.extensionName} extension` },
        },
      },
      vueI18nMessages: {
        en: {
          EXTENSION_NAME: input.extensionName,
          EXTENSION_DESCRIPTION: `${input.extensionName} extension`,
        },
      },
    };
  } catch (error) {
    logger.error(`Failed to process locales for variant ${input.variantTarget}:`, error);
    throw error;
  }
}

// Re-export types for convenience
export type {
  LocaleData,
  ChromeMessageEntry,
  I18nInput,
  I18nOutput,
} from '../extension-config/types.js';
