/**
 * @fileoverview I18n module main entry point
 * @description Provides internationalization processing functionality
 */

import path from 'path';
import { createLogger } from '../utils/logger.js';
import { utils } from '../utils/index.js';
import { 
  DEFAULT_CHROME_LOCALES_ONLY, 
  DEFAULT_CHROME_MESSAGES_ONLY,
  PLACEHOLDER_PATTERNS,
  I18N_ERROR_MESSAGES 
} from './constants.js';

import type {
  LocaleData,
  ChromeMessageEntry,
  I18nInput,
  I18nOutput,
  ProcessedVariantConfig,
} from '../extension-config/types.js';

const logger = createLogger('I18n');

/**
 * Load shared locale data from packages/shared/locales
 */
export async function loadSharedLocales(): Promise<LocaleData> {
  const sharedLocalesDir = utils.paths.getSharedLocalesDir();
  
  if (!await utils.fs.exists(sharedLocalesDir)) {
    logger.warn(`Shared locales directory not found: ${sharedLocalesDir}`);
    return {};
  }
  
  const localeFiles = await utils.glob.findLocaleFiles(sharedLocalesDir);
  const sharedLocales: LocaleData = {};
  
  for (const filePath of localeFiles) {
    const locale = path.basename(filePath, '.json');
    
    try {
      const data = await utils.fs.readJson<Record<string, ChromeMessageEntry>>(filePath);
      sharedLocales[locale] = data;
      logger.verbose(`Loaded shared locale: ${locale}`);
    } catch (error) {
      logger.error(`Failed to load shared locale ${locale}:`, error);
      throw new Error(I18N_ERROR_MESSAGES.INVALID_JSON(filePath));
    }
  }
  
  logger.info(`Loaded ${Object.keys(sharedLocales).length} shared locales`);
  return sharedLocales;
}

/**
 * Load extension-specific locale data
 */
export async function loadExtensionLocales(extensionName: string): Promise<LocaleData> {
  const extensionLocalesDir = utils.paths.getExtensionLocalesDir(extensionName);
  
  if (!await utils.fs.exists(extensionLocalesDir)) {
    logger.warn(`Extension locales directory not found: ${extensionLocalesDir}`);
    return {};
  }
  
  const localeFiles = await utils.glob.findLocaleFiles(extensionLocalesDir);
  const extensionLocales: LocaleData = {};
  
  for (const filePath of localeFiles) {
    const locale = path.basename(filePath, '.json');
    
    try {
      const data = await utils.fs.readJson<Record<string, ChromeMessageEntry>>(filePath);
      extensionLocales[locale] = data;
      logger.verbose(`Loaded extension locale: ${locale} for ${extensionName}`);
    } catch (error) {
      logger.error(`Failed to load extension locale ${locale} for ${extensionName}:`, error);
      throw new Error(I18N_ERROR_MESSAGES.INVALID_JSON(filePath));
    }
  }
  
  logger.info(`Loaded ${Object.keys(extensionLocales).length} extension locales for ${extensionName}`);
  return extensionLocales;
}

/**
 * Get available locales for a specific extension
 */
export async function getAvailableLocales(extensionName: string): Promise<string[]> {
  const [sharedLocales, extensionLocales] = await Promise.all([
    loadSharedLocales(),
    loadExtensionLocales(extensionName)
  ]);
  
  const allLocales = new Set([
    ...Object.keys(sharedLocales),
    ...Object.keys(extensionLocales)
  ]);
  
  return Array.from(allLocales).sort();
}

/**
 * Process locales for a specific variant
 * This is the main function that generates the final i18n output
 */
export async function processLocalesForVariant(input: I18nInput): Promise<I18nOutput> {
  logger.info(`Processing locales for variant: ${input.variantTarget}`);
  
  try {
    // Load raw locale data
    const [sharedLocales, extensionLocales] = await Promise.all([
      loadSharedLocales(),
      loadExtensionLocales(input.extensionName)
    ]);
    
    // Merge shared and extension locales (extension takes precedence)
    const mergedLocales = mergeLocaleData(sharedLocales, extensionLocales);
    
    // Get available locales
    const availableLocales = Object.keys(mergedLocales).sort();
    
    if (availableLocales.length === 0) {
      logger.warn(`No locales found for extension: ${input.extensionName}`);
      return {
        chromeMessages: {},
        vueI18nMessages: {},
      };
    }
    
    // Process messages for each locale
    const chromeMessages: I18nOutput['chromeMessages'] = {};
    const vueI18nMessages: I18nOutput['vueI18nMessages'] = {};
    
    for (const locale of availableLocales) {
      const localeData = mergedLocales[locale];
      
      // Process messages with variant-specific overrides
      const processedMessages = processMessagesForVariant(localeData, input.variantTarget);
      
      // Apply filters
      const filteredMessages = applyMessageFilters(processedMessages, {
        includes: input.i18nConfig.includes,
        excludes: input.i18nConfig.excludes,
      });
      
      // Generate Chrome messages
      const chromeOnlyMessages = applyMessageFilters(filteredMessages, {
        includes: input.i18nConfig.chromeMessagesOnly,
      });
      
      chromeMessages[locale] = generateChromeMessages(chromeOnlyMessages);
      
      // Generate Vue i18n messages (exclude Chrome-only locales)
      const isChromeOnlyLocale = input.i18nConfig.chromeLocalesOnly.includes(locale);
      if (!isChromeOnlyLocale) {
        vueI18nMessages[locale] = generateVueI18nMessages(filteredMessages);
      }
    }
    
    logger.success(`Processed ${availableLocales.length} locales for ${input.variantTarget}`);
    
    return {
      chromeMessages,
      vueI18nMessages,
    };
    
  } catch (error) {
    logger.error(`Failed to process locales for variant ${input.variantTarget}:`, error);
    throw error;
  }
}

/**
 * Merge shared and extension locale data
 */
function mergeLocaleData(sharedLocales: LocaleData, extensionLocales: LocaleData): LocaleData {
  const merged: LocaleData = {};
  
  // Get all unique locales
  const allLocales = new Set([
    ...Object.keys(sharedLocales),
    ...Object.keys(extensionLocales)
  ]);
  
  for (const locale of allLocales) {
    const sharedData = sharedLocales[locale] || {};
    const extensionData = extensionLocales[locale] || {};
    
    // Extension data takes precedence over shared data
    merged[locale] = {
      ...sharedData,
      ...extensionData,
    };
  }
  
  return merged;
}

/**
 * Process messages with variant-specific overrides
 */
function processMessagesForVariant(
  localeData: Record<string, ChromeMessageEntry>,
  variantTarget: string
): Record<string, ChromeMessageEntry> {
  const processed: Record<string, ChromeMessageEntry> = {};
  
  for (const [key, entry] of Object.entries(localeData)) {
    // Create a copy of the entry
    const processedEntry: ChromeMessageEntry = {
      message: entry.message,
      description: entry.description,
      placeholders: entry.placeholders,
    };
    
    // Check for variant-specific override
    if (entry[variantTarget]) {
      processedEntry.message = entry[variantTarget];
      logger.verbose(`Applied variant override for ${key} in ${variantTarget}`);
    }
    
    processed[key] = processedEntry;
  }
  
  return processed;
}

/**
 * Apply include/exclude filters to messages
 */
function applyMessageFilters(
  messages: Record<string, ChromeMessageEntry>,
  filters: {
    includes?: string[];
    excludes?: string[];
  }
): Record<string, ChromeMessageEntry> {
  
  const keys = Object.keys(messages);
  const filteredKeys = utils.string.filterByPatterns(keys, filters);
  
  const filtered: Record<string, ChromeMessageEntry> = {};
  for (const key of filteredKeys) {
    filtered[key] = messages[key];
  }
  
  return filtered;
}

/**
 * Generate Chrome extension messages format
 */
function generateChromeMessages(
  messages: Record<string, ChromeMessageEntry>
): I18nOutput['chromeMessages'][string] {
  const chromeMessages: I18nOutput['chromeMessages'][string] = {};
  
  for (const [key, entry] of Object.entries(messages)) {
    const chromeEntry: I18nOutput['chromeMessages'][string][string] = {
      message: entry.message,
    };
    
    if (entry.description) {
      chromeEntry.description = entry.description;
    }
    
    // Process placeholders
    if (entry.placeholders) {
      chromeEntry.placeholders = entry.placeholders;
    } else {
      // Auto-generate placeholders if they don't exist
      const placeholders = extractPlaceholders(entry.message);
      if (placeholders.length > 0) {
        chromeEntry.placeholders = {};
        placeholders.forEach((placeholder, index) => {
          chromeEntry.placeholders![placeholder] = {
            content: `$${index + 1}`,
          };
        });
      }
    }
    
    chromeMessages[key] = chromeEntry;
  }
  
  return chromeMessages;
}

/**
 * Generate Vue i18n messages format
 */
function generateVueI18nMessages(
  messages: Record<string, ChromeMessageEntry>
): I18nOutput['vueI18nMessages'][string] {
  const vueMessages: I18nOutput['vueI18nMessages'][string] = {};
  
  for (const [key, entry] of Object.entries(messages)) {
    // Convert Chrome placeholder format to Vue i18n format
    const vueMessage = convertPlaceholdersToVueFormat(entry.message);
    vueMessages[key] = vueMessage;
  }
  
  return vueMessages;
}

/**
 * Extract placeholders from a message string
 */
function extractPlaceholders(message: string): string[] {
  const matches = message.match(PLACEHOLDER_PATTERNS.CHROME_TO_VUE);
  if (!matches) return [];
  
  return matches.map(match => match.replace(/\$/g, ''));
}

/**
 * Convert Chrome placeholder format to Vue i18n format
 * Example: "Hello $name$" -> "Hello {name}"
 */
function convertPlaceholdersToVueFormat(message: string): string {
  return message.replace(PLACEHOLDER_PATTERNS.CHROME_TO_VUE, '{$1}');
}

/**
 * Convert Vue i18n placeholder format to Chrome format
 * Example: "Hello {name}" -> "Hello $name$"
 */
function convertPlaceholdersToChromeFormat(message: string): string {
  return message.replace(PLACEHOLDER_PATTERNS.VUE_TO_CHROME, '$$1$');
}

/**
 * Validate locale data
 */
export function validateLocaleData(localeData: LocaleData): void {
  for (const [locale, messages] of Object.entries(localeData)) {
    if (!utils.validation.isValidLocale(locale)) {
      throw new Error(I18N_ERROR_MESSAGES.INVALID_LOCALE_FORMAT(locale));
    }
    
    for (const [key, entry] of Object.entries(messages)) {
      if (!entry.message) {
        throw new Error(I18N_ERROR_MESSAGES.MISSING_MESSAGE_KEY(key, locale));
      }
      
      if (entry.message.length > 512) {
        throw new Error(`Message too long for key '${key}' in locale '${locale}': ${entry.message.length} characters`);
      }
    }
  }
}

/**
 * Get locale statistics
 */
export function getLocaleStats(localeData: LocaleData): {
  totalLocales: number;
  totalKeys: number;
  keysByLocale: Record<string, number>;
  missingTranslations: Record<string, string[]>;
} {
  const stats = {
    totalLocales: Object.keys(localeData).length,
    totalKeys: 0,
    keysByLocale: {} as Record<string, number>,
    missingTranslations: {} as Record<string, string[]>,
  };
  
  // Get all unique keys
  const allKeys = new Set<string>();
  for (const messages of Object.values(localeData)) {
    for (const key of Object.keys(messages)) {
      allKeys.add(key);
    }
  }
  
  stats.totalKeys = allKeys.size;
  
  // Calculate statistics for each locale
  for (const [locale, messages] of Object.entries(localeData)) {
    const keys = Object.keys(messages);
    stats.keysByLocale[locale] = keys.length;
    
    // Find missing translations
    const missingKeys = Array.from(allKeys).filter(key => !(key in messages));
    if (missingKeys.length > 0) {
      stats.missingTranslations[locale] = missingKeys;
    }
  }
  
  return stats;
}

// Re-export types for convenience
export type {
  LocaleData,
  ChromeMessageEntry,
  I18nInput,
  I18nOutput,
} from '../extension-config/types.js';