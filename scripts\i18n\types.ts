/**
 * @fileoverview I18n module type definitions
 * @description Defines internationalization processing related type structures
 */

// Import base types from extension-config module
export type {
  LocaleData,
  ChromeMessageEntry,
  I18nInput,
  I18nOutput,
  ProcessedVariantConfig
} from '../extension-config/types.js';

/**
 * Locale loading configuration
 */
export interface LocaleLoadConfig {
  /**
   * Extension name
   */
  extensionName: string;
  
  /**
   * Whether to include shared locale packages
   */
  includeShared?: boolean;
  
  /**
   * Specific language list, if not specified then load all available languages
   */
  locales?: string[];
}

/**
 * Message filtering configuration
 */
export interface MessageFilterConfig {
  /**
   * Included message key patterns (supports regex)
   */
  includes?: string[];
  
  /**
   * Excluded message key patterns (supports regex)
   */
  excludes?: string[];
  
  /**
   * Message key patterns only for Chrome (supports regex)
   */
  chromeMessagesOnly?: string[];
  
  /**
   * Language codes only for Chrome
   */
  chromeLocalesOnly?: string[];
}

/**
 * Message transformation configuration
 */
export interface MessageTransformConfig {
  /**
   * Target variant identifier
   */
  variantTarget: string;
  
  /**
   * Whether to transform placeholder format (Chrome format -> vue-i18n format)
   */
  transformPlaceholders?: boolean;
  
  /**
   * Whether to flatten object structure
   */
  flattenMessages?: boolean;
}

/**
 * Locale validation result
 */
export interface LocaleValidationResult {
  /**
   * Whether validation passed
   */
  isValid: boolean;
  
  /**
   * Error message list
   */
  errors: string[];
  
  /**
   * Warning message list
   */
  warnings: string[];
  
  /**
   * Statistics information
   */
  stats: {
    totalKeys: number;
    missingKeys: number;
    emptyValues: number;
    duplicateKeys: number;
  };
}

/**
 * Locale statistics information
 */
export interface LocaleStats {
  /**
   * Total number of languages
   */
  totalLocales: number;
  
  /**
   * Total number of message keys
   */
  totalKeys: number;
  
  /**
   * Key count statistics for each language
   */
  keysByLocale: Record<string, number>;
  
  /**
   * Missing translation statistics
   */
  missingTranslations: {
    [locale: string]: string[];
  };
}

/**
 * Conditional message parsing result
 */
export interface ConditionalMessageResult {
  /**
   * Final message text used
   */
  message: string;
  
  /**
   * Whether conditional override was used
   */
  isOverridden: boolean;
  
  /**
   * Condition key name used
   */
  conditionKey?: string;
}