/**
 * @fileoverview Manifest module constant definitions
 * @description Defines Manifest generation related constants
 */

import type { ManifestVersionSpecificConfig, PermissionProcessingConfig } from './types.js';

/**
 * Manifest fields that need to be force overridden
 */
export const FORCE_OVERRIDE_FIELDS = [
  'manifest_version',
  'version',
  'default_locale',
] as const;

/**
 * Manifest fields that need internationalization
 */
export const I18N_MANIFEST_FIELDS = {
  name: 'EXTENSION_NAME',
  description: 'EXTENSION_DESCRIPTION',
  short_name: 'EXTENSION_SHORT_NAME',
} as const;

/**
 * Manifest version specific configuration
 */
export const MANIFEST_VERSION_CONFIG: ManifestVersionSpecificConfig = {
  v2: {
    requiredFields: [
      'manifest_version',
      'name',
      'version',
    ],
    deprecatedFields: [
      'browser_action',
      'page_action',
      'background.scripts',
      'background.persistent',
    ],
    supportedPermissions: [
      'activeTab',
      'alarms',
      'background',
      'bookmarks',
      'browsingData',
      'clipboardRead',
      'clipboardWrite',
      'contextMenus',
      'cookies',
      'declarativeContent',
      'desktopCapture',
      'downloads',
      'fontSettings',
      'geolocation',
      'history',
      'identity',
      'idle',
      'management',
      'nativeMessaging',
      'notifications',
      'pageCapture',
      'power',
      'privacy',
      'proxy',
      'pushMessaging',
      'sessions',
      'storage',
      'system.cpu',
      'system.memory',
      'system.storage',
      'tabCapture',
      'tabs',
      'topSites',
      'tts',
      'ttsEngine',
      'unlimitedStorage',
      'webNavigation',
      'webRequest',
      'webRequestBlocking',
    ],
  },
  v3: {
    requiredFields: [
      'manifest_version',
      'name',
      'version',
    ],
    newFields: [
      'action',
      'background.service_worker',
      'host_permissions',
      'optional_host_permissions',
    ],
    supportedPermissions: [
      'activeTab',
      'alarms',
      'background',
      'bookmarks',
      'browsingData',
      'clipboardRead',
      'clipboardWrite',
      'contextMenus',
      'cookies',
      'declarativeContent',
      'declarativeNetRequest',
      'desktopCapture',
      'downloads',
      'fontSettings',
      'geolocation',
      'history',
      'identity',
      'idle',
      'management',
      'nativeMessaging',
      'notifications',
      'offscreen',
      'pageCapture',
      'power',
      'privacy',
      'proxy',
      'scripting',
      'search',
      'sessions',
      'sidePanel',
      'storage',
      'system.cpu',
      'system.memory',
      'system.storage',
      'tabCapture',
      'tabs',
      'topSites',
      'tts',
      'ttsEngine',
      'unlimitedStorage',
      'webNavigation',
      'webRequest',
    ],
    hostPermissions: [
      'http://*/*',
      'https://*/*',
      'file://*/*',
    ],
  },
};

/**
 * Base permission processing configuration
 */
export const PERMISSION_CONFIG: PermissionProcessingConfig = {
  basePermissions: [
    'storage',
    'activeTab',
  ],
  browserSpecificPermissions: {
    chrome: {
      supported: [
        'alarms',
        'background',
        'bookmarks',
        'browsingData',
        'clipboardRead',
        'clipboardWrite',
        'contextMenus',
        'cookies',
        'declarativeContent',
        'declarativeNetRequest',
        'desktopCapture',
        'downloads',
        'fontSettings',
        'geolocation',
        'history',
        'identity',
        'idle',
        'management',
        'nativeMessaging',
        'notifications',
        'offscreen',
        'pageCapture',
        'power',
        'privacy',
        'proxy',
        'scripting',
        'search',
        'sessions',
        'sidePanel',
        'storage',
        'system.cpu',
        'system.memory',
        'system.storage',
        'tabCapture',
        'tabs',
        'topSites',
        'tts',
        'ttsEngine',
        'unlimitedStorage',
        'webNavigation',
        'webRequest',
      ],
      unsupported: [],
      alternatives: {},
    },
    firefox: {
      supported: [
        'alarms',
        'background',
        'bookmarks',
        'browsingData',
        'clipboardRead',
        'clipboardWrite',
        'contextMenus',
        'cookies',
        'downloads',
        'fontSettings',
        'geolocation',
        'history',
        'identity',
        'idle',
        'management',
        'nativeMessaging',
        'notifications',
        'pageCapture',
        'power',
        'privacy',
        'proxy',
        'sessions',
        'storage',
        'tabCapture',
        'tabs',
        'topSites',
        'unlimitedStorage',
        'webNavigation',
        'webRequest',
        'webRequestBlocking',
      ],
      unsupported: [
        'declarativeContent',
        'declarativeNetRequest',
        'desktopCapture',
        'offscreen',
        'scripting',
        'search',
        'sidePanel',
        'system.cpu',
        'system.memory',
        'system.storage',
        'tts',
        'ttsEngine',
      ],
      alternatives: {
        'declarativeContent': 'activeTab',
        'declarativeNetRequest': 'webRequest',
      },
    },
    edge: {
      supported: [
        'alarms',
        'background',
        'bookmarks',
        'browsingData',
        'clipboardRead',
        'clipboardWrite',
        'contextMenus',
        'cookies',
        'declarativeContent',
        'declarativeNetRequest',
        'desktopCapture',
        'downloads',
        'fontSettings',
        'geolocation',
        'history',
        'identity',
        'idle',
        'management',
        'nativeMessaging',
        'notifications',
        'pageCapture',
        'power',
        'privacy',
        'proxy',
        'scripting',
        'search',
        'sessions',
        'storage',
        'system.cpu',
        'system.memory',
        'system.storage',
        'tabCapture',
        'tabs',
        'topSites',
        'tts',
        'ttsEngine',
        'unlimitedStorage',
        'webNavigation',
        'webRequest',
      ],
      unsupported: [
        'offscreen',
        'sidePanel',
      ],
      alternatives: {},
    },
  },
  hostPermissions: {
    development: [
      'http://localhost/*',
      'https://localhost/*',
    ],
    production: [
      'https://*/*',
    ],
  },
};

/**
 * Browser-specific processing rules
 */
export const BROWSER_SPECIFIC_RULES = {
  chrome: {
    manifestKeys: ['update_url', 'key'],
    requiredFields: [],
    optionalFields: ['externally_connectable'],
  },
  firefox: {
    manifestKeys: ['browser_specific_settings'],
    requiredFields: [],
    optionalFields: ['developer'],
  },
  edge: {
    manifestKeys: ['update_url'],
    requiredFields: [],
    optionalFields: ['externally_connectable'],
  },
} as const;

/**
 * Error messages
 */
export const MANIFEST_ERROR_MESSAGES = {
  MISSING_REQUIRED_FIELD: (field: string) => `Required manifest field '${field}' is missing`,
  INVALID_MANIFEST_VERSION: (version: number) => `Invalid manifest version: ${version}`,
  INVALID_PERMISSION: (permission: string, browser: string) => `Permission '${permission}' is not supported in ${browser}`,
  INVALID_HOST_PERMISSION: (permission: string) => `Invalid host permission format: ${permission}`,
  FIELD_NOT_SUPPORTED: (field: string, version: number) => `Field '${field}' is not supported in manifest v${version}`,
  VALIDATION_FAILED: 'Manifest validation failed',
  BROWSER_NOT_SUPPORTED: (browser: string) => `Browser '${browser}' is not supported`,
} as const;

/**
 * Success messages
 */
export const MANIFEST_SUCCESS_MESSAGES = {
  MANIFEST_GENERATED: 'Manifest generated successfully',
  VALIDATION_PASSED: 'Manifest validation passed',
  PERMISSIONS_PROCESSED: 'Permissions processed successfully',
} as const;

/**
 * Debug related constants
 */
export const DEBUG_CONFIG = {
  VERBOSE_LOGGING: false,
  PRESERVE_COMMENTS: false,
  VALIDATE_PERMISSIONS: true,
  STRICT_MODE: false,
} as const;