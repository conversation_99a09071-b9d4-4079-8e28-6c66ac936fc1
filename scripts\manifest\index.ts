/**
 * @fileoverview Manifest module main entry point
 * @description Provides manifest generation and processing functionality
 */

import { createLogger } from '../utils/logger.js';
import { utils } from '../utils/index.js';
import { 
  FORCE_OVERRIDE_FIELDS,
  I18N_MANIFEST_FIELDS,
  MANIFEST_VERSION_CONFIG,
  PERMISSION_CONFIG,
  BROWSER_SPECIFIC_RULES,
  MANIFEST_ERROR_MESSAGES,
  MANIFEST_SUCCESS_MESSAGES 
} from './constants.js';

import type {
  ManifestInput,
  FinalManifest,
  ManifestValidationResult,
  ManifestGenerationConfig,
  VariantInfo,
} from './types.js';

const logger = createLogger('Manifest');

/**
 * Generate manifest for a specific variant
 * This is the main function that creates the final manifest.json content
 */
export function generateManifestForVariant(input: ManifestInput): FinalManifest {
  logger.info(`Generating manifest for variant: ${input.variantInfo.variantId}`);
  
  try {
    // Start with the base manifest
    const manifest = { ...input.baseManifest };
    
    // Apply force overrides
    applyForceOverrides(manifest, input.variantInfo);
    
    // Apply internationalization
    applyI18nFields(manifest);
    
    // Apply browser-specific rules
    applyBrowserSpecificRules(manifest, input.variantInfo);
    
    // Apply manifest version-specific rules
    applyVersionSpecificRules(manifest, input.variantInfo);
    
    // Process permissions
    processPermissions(manifest, input.variantInfo);
    
    // Validate the final manifest
    const validationResult = validateManifest(manifest as FinalManifest, input.variantInfo);
    if (!validationResult.isValid) {
      throw new Error(`Manifest validation failed: ${validationResult.errors.join(', ')}`);
    }
    
    logger.success(`Generated manifest for ${input.variantInfo.variantId}`);
    return manifest as FinalManifest;
    
  } catch (error) {
    logger.error(`Failed to generate manifest for variant ${input.variantInfo.variantId}:`, error);
    throw error;
  }
}

/**
 * Apply force overrides to manifest fields
 */
function applyForceOverrides(manifest: any, variantInfo: VariantInfo): void {
  // Force override version, manifest_version, and default_locale
  manifest.version = variantInfo.version;
  manifest.manifest_version = variantInfo.manifestVersion;
  manifest.default_locale = variantInfo.defaultLocale;
  
  logger.verbose(`Applied force overrides: version=${variantInfo.version}, manifest_version=${variantInfo.manifestVersion}, default_locale=${variantInfo.defaultLocale}`);
}

/**
 * Apply internationalization to manifest fields
 */
function applyI18nFields(manifest: any): void {
  // Replace certain fields with i18n message references
  for (const [field, i18nKey] of Object.entries(I18N_MANIFEST_FIELDS)) {
    if (manifest[field] !== undefined) {
      manifest[field] = `__MSG_${i18nKey}__`;
    }
  }
  
  // If name is not set, set it to the default i18n key
  if (!manifest.name) {
    manifest.name = `__MSG_${I18N_MANIFEST_FIELDS.name}__`;
  }
  
  // If description is not set, set it to the default i18n key
  if (!manifest.description) {
    manifest.description = `__MSG_${I18N_MANIFEST_FIELDS.description}__`;
  }
  
  logger.verbose('Applied i18n fields to manifest');
}

/**
 * Apply browser-specific rules
 */
function applyBrowserSpecificRules(manifest: any, variantInfo: VariantInfo): void {
  const browserRules = BROWSER_SPECIFIC_RULES[variantInfo.webstore as keyof typeof BROWSER_SPECIFIC_RULES];
  
  if (!browserRules) {
    logger.warn(`No browser-specific rules found for ${variantInfo.webstore}`);
    return;
  }
  
  // Remove unsupported keys for this browser
  const allBrowserKeys = Object.values(BROWSER_SPECIFIC_RULES).flatMap(rules => rules.manifestKeys);
  const supportedKeys = browserRules.manifestKeys;
  
  for (const key of allBrowserKeys) {
    if (!supportedKeys.includes(key) && manifest[key] !== undefined) {
      delete manifest[key];
      logger.verbose(`Removed unsupported manifest key for ${variantInfo.webstore}: ${key}`);
    }
  }
  
  logger.verbose(`Applied browser-specific rules for ${variantInfo.webstore}`);
}

/**
 * Apply manifest version-specific rules
 */
function applyVersionSpecificRules(manifest: any, variantInfo: VariantInfo): void {
  const versionConfig = MANIFEST_VERSION_CONFIG[`v${variantInfo.manifestVersion}` as keyof typeof MANIFEST_VERSION_CONFIG];
  
  if (!versionConfig) {
    throw new Error(MANIFEST_ERROR_MESSAGES.INVALID_MANIFEST_VERSION(variantInfo.manifestVersion));
  }
  
  // Check for deprecated fields in V3
  if (variantInfo.manifestVersion === 3) {
    const v2Config = MANIFEST_VERSION_CONFIG.v2;
    for (const deprecatedField of v2Config.deprecatedFields) {
      if (utils.object.has(manifest, deprecatedField)) {
        logger.warn(`Deprecated field '${deprecatedField}' found in manifest v3 and will be removed`);
        // Remove the deprecated field
        const keys = deprecatedField.split('.');
        if (keys.length === 1) {
          delete manifest[keys[0]];
        } else {
          // Handle nested fields like 'background.scripts'
          const parentKey = keys[0];
          const childKey = keys[1];
          if (manifest[parentKey] && manifest[parentKey][childKey]) {
            delete manifest[parentKey][childKey];
          }
        }
      }
    }
  }
  
  // Apply version-specific field defaults
  const versionSpecificFields = versionConfig.versionSpecificFields;
  if (versionSpecificFields) {
    const fieldsToApply = versionSpecificFields[`v${variantInfo.manifestVersion}` as keyof typeof versionSpecificFields];
    if (fieldsToApply) {
      Object.assign(manifest, fieldsToApply);
    }
  }
  
  logger.verbose(`Applied manifest v${variantInfo.manifestVersion} specific rules`);
}

/**
 * Process permissions for the target browser
 */
function processPermissions(manifest: any, variantInfo: VariantInfo): void {
  if (!manifest.permissions) {
    manifest.permissions = [];
  }
  
  const browserPermissions = PERMISSION_CONFIG.browserSpecificPermissions[variantInfo.webstore];
  
  if (!browserPermissions) {
    logger.warn(`No permission configuration found for ${variantInfo.webstore}`);
    return;
  }
  
  // Filter out unsupported permissions
  const originalPermissions = [...manifest.permissions];
  const filteredPermissions = [];
  
  for (const permission of originalPermissions) {
    if (browserPermissions.supported.includes(permission)) {
      filteredPermissions.push(permission);
    } else if (browserPermissions.unsupported.includes(permission)) {
      const alternative = browserPermissions.alternatives[permission];
      if (alternative) {
        filteredPermissions.push(alternative);
        logger.warn(`Replaced unsupported permission '${permission}' with '${alternative}' for ${variantInfo.webstore}`);
      } else {
        logger.warn(`Removed unsupported permission '${permission}' for ${variantInfo.webstore}`);
      }
    } else {
      // Permission not in supported or unsupported list, keep it
      filteredPermissions.push(permission);
    }
  }
  
  manifest.permissions = utils.array.unique(filteredPermissions);
  
  // Handle host permissions for manifest v3
  if (variantInfo.manifestVersion === 3) {
    if (manifest.host_permissions) {
      // Validate host permissions
      const validHostPermissions = manifest.host_permissions.filter((permission: string) => {
        return isValidHostPermission(permission);
      });
      
      if (validHostPermissions.length !== manifest.host_permissions.length) {
        logger.warn(`Removed invalid host permissions for ${variantInfo.webstore}`);
        manifest.host_permissions = validHostPermissions;
      }
    }
  }
  
  logger.verbose(`Processed permissions for ${variantInfo.webstore}`);
}

/**
 * Validate host permission format
 */
function isValidHostPermission(permission: string): boolean {
  // Basic validation for host permissions
  const hostPermissionPatterns = [
    /^https?:\/\/\*\/\*$/,
    /^https?:\/\/\*\.\w+\/\*$/,
    /^https?:\/\/[\w.-]+\/\*$/,
    /^file:\/\/\/\*$/,
    /^ftp:\/\/\*\/\*$/,
  ];
  
  return hostPermissionPatterns.some(pattern => pattern.test(permission));
}

/**
 * Validate a manifest object
 */
export function validateManifest(manifest: FinalManifest, variantInfo: VariantInfo): ManifestValidationResult {
  const result: ManifestValidationResult = {
    isValid: true,
    errors: [],
    warnings: [],
    fieldValidations: {},
  };
  
  try {
    // Check required fields
    const versionConfig = MANIFEST_VERSION_CONFIG[`v${variantInfo.manifestVersion}` as keyof typeof MANIFEST_VERSION_CONFIG];
    
    for (const field of versionConfig.requiredFields) {
      if (!manifest[field as keyof FinalManifest]) {
        result.errors.push(MANIFEST_ERROR_MESSAGES.MISSING_REQUIRED_FIELD(field));
        result.fieldValidations[field] = {
          isValid: false,
          error: MANIFEST_ERROR_MESSAGES.MISSING_REQUIRED_FIELD(field),
        };
      } else {
        result.fieldValidations[field] = {
          isValid: true,
        };
      }
    }
    
    // Validate manifest version
    if (manifest.manifest_version !== variantInfo.manifestVersion) {
      result.errors.push(`Manifest version mismatch: expected ${variantInfo.manifestVersion}, got ${manifest.manifest_version}`);
    }
    
    // Validate permissions
    if (manifest.permissions) {
      const browserPermissions = PERMISSION_CONFIG.browserSpecificPermissions[variantInfo.webstore];
      if (browserPermissions) {
        for (const permission of manifest.permissions) {
          if (!browserPermissions.supported.includes(permission)) {
            result.warnings.push(`Permission '${permission}' may not be supported in ${variantInfo.webstore}`);
          }
        }
      }
    }
    
    // Validate host permissions for v3
    if (variantInfo.manifestVersion === 3 && manifest.host_permissions) {
      for (const permission of manifest.host_permissions) {
        if (!isValidHostPermission(permission)) {
          result.errors.push(MANIFEST_ERROR_MESSAGES.INVALID_HOST_PERMISSION(permission));
        }
      }
    }
    
    result.isValid = result.errors.length === 0;
    
    if (result.isValid) {
      logger.verbose(MANIFEST_SUCCESS_MESSAGES.VALIDATION_PASSED);
    }
    
  } catch (error) {
    result.isValid = false;
    result.errors.push(`Validation error: ${error}`);
    logger.error('Manifest validation error:', error);
  }
  
  return result;
}

/**
 * Generate a basic manifest template
 */
export function generateManifestTemplate(
  variantInfo: VariantInfo,
  options: ManifestGenerationConfig = {}
): Partial<FinalManifest> {
  const template: Partial<FinalManifest> = {
    manifest_version: variantInfo.manifestVersion,
    name: `__MSG_${I18N_MANIFEST_FIELDS.name}__`,
    description: `__MSG_${I18N_MANIFEST_FIELDS.description}__`,
    version: variantInfo.version,
    default_locale: variantInfo.defaultLocale,
    permissions: [...PERMISSION_CONFIG.basePermissions],
  };
  
  // Add version-specific fields
  if (variantInfo.manifestVersion === 3) {
    template.action = {
      default_popup: 'popup.html',
    };
    template.background = {
      service_worker: 'background.js',
    };
    template.host_permissions = [...PERMISSION_CONFIG.hostPermissions.production];
  } else {
    template.browser_action = {
      default_popup: 'popup.html',
    };
    template.background = {
      scripts: ['background.js'],
      persistent: false,
    };
  }
  
  // Add icons
  template.icons = {
    16: 'icons/16.png',
    32: 'icons/32.png',
    48: 'icons/48.png',
    128: 'icons/128.png',
  };
  
  logger.info(`Generated manifest template for ${variantInfo.variantId}`);
  return template;
}

/**
 * Compare two manifests and return differences
 */
export function compareManifests(
  manifest1: FinalManifest,
  manifest2: FinalManifest
): {
  added: Record<string, any>;
  removed: Record<string, any>;
  changed: Record<string, { from: any; to: any }>;
} {
  const result = {
    added: {} as Record<string, any>,
    removed: {} as Record<string, any>,
    changed: {} as Record<string, { from: any; to: any }>,
  };
  
  // Get all keys from both manifests
  const allKeys = new Set([...Object.keys(manifest1), ...Object.keys(manifest2)]);
  
  for (const key of allKeys) {
    const value1 = manifest1[key as keyof FinalManifest];
    const value2 = manifest2[key as keyof FinalManifest];
    
    if (value1 === undefined && value2 !== undefined) {
      result.added[key] = value2;
    } else if (value1 !== undefined && value2 === undefined) {
      result.removed[key] = value1;
    } else if (JSON.stringify(value1) !== JSON.stringify(value2)) {
      result.changed[key] = { from: value1, to: value2 };
    }
  }
  
  return result;
}

// Re-export types for convenience
export type {
  ManifestInput,
  FinalManifest,
  ManifestValidationResult,
  ManifestGenerationConfig,
  VariantInfo,
} from './types.js';