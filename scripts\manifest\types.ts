/**
 * @fileoverview Manifest module type definitions
 * @description Defines Manifest generation processing related type structures
 */

// Import base types from extension-config module
export type {
  ManifestInput,
  FinalManifest,
  ProcessedVariantConfig,
} from '../extension-config/types.js';

import type { <PERSON>rows<PERSON> } from 'wxt/browser';

/**
 * Manifest field automatic processing rules
 */
export interface ManifestFieldProcessingRules {
  /**
   * Fields that need to be force overridden
   */
  forceOverride: {
    [K in keyof Browser.runtime.Manifest]?: (
      variantInfo: VariantInfo,
    ) => Browser.runtime.Manifest[K];
  };

  /**
   * Fields that need internationalization processing
   */
  i18nFields: {
    [K in keyof Browser.runtime.Manifest]?: string; // Corresponding i18n key
  };

  /**
   * Fields that need conditional processing based on manifest version
   */
  versionSpecificFields: {
    v2?: Partial<Browser.runtime.Manifest>;
    v3?: Partial<Browser.runtime.Manifest>;
  };
}

/**
 * Variant information simplified structure
 */
export interface VariantInfo {
  name: string;
  version: string;
  manifestVersion: 2 | 3;
  defaultLocale: string;
  variantId: string;
  variantName: string;
  variantType: string;
  webstore: string;
  measurementId?: string;
}

/**
 * Manifest generation configuration
 */
export interface ManifestGenerationConfig {
  /**
   * Whether to enable debug mode
   */
  debug?: boolean;

  /**
   * Whether to validate generated manifest
   */
  validate?: boolean;

  /**
   * Custom processing rules
   */
  customRules?: Partial<ManifestFieldProcessingRules>;

  /**
   * Whether to preserve original field comments
   */
  preserveComments?: boolean;
}

/**
 * Manifest validation result
 */
export interface ManifestValidationResult {
  /**
   * Whether validation passed
   */
  isValid: boolean;

  /**
   * Error message list
   */
  errors: string[];

  /**
   * Warning message list
   */
  warnings: string[];

  /**
   * Validation details for each field
   */
  fieldValidations: {
    [field: string]: {
      isValid: boolean;
      error?: string;
      warning?: string;
    };
  };
}

/**
 * Supported manifest version-specific configuration
 */
export interface ManifestVersionSpecificConfig {
  /**
   * V2 specific configuration
   */
  v2: {
    requiredFields: string[];
    deprecatedFields: string[];
    supportedPermissions: string[];
  };

  /**
   * V3 specific configuration
   */
  v3: {
    requiredFields: string[];
    newFields: string[];
    supportedPermissions: string[];
    hostPermissions: string[];
  };
}

/**
 * Permission processing configuration
 */
export interface PermissionProcessingConfig {
  /**
   * Base permissions list
   */
  basePermissions: string[];

  /**
   * Browser-specific permission mapping
   */
  browserSpecificPermissions: {
    [browser: string]: {
      supported: string[];
      unsupported: string[];
      alternatives: Record<string, string>;
    };
  };

  /**
   * Host permission processing
   */
  hostPermissions: {
    development: string[];
    production: string[];
  };
}