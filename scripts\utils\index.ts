/**
 * @fileoverview Utility functions for the scaffolding system
 * @description Common utility functions used across all modules
 */

import fs from 'fs-extra';
import path from 'path';
import { glob } from 'glob';
import { createLogger } from './logger.js';

const logger = createLogger('Utils');

/**
 * Path utilities
 */
export const paths = {
  /**
   * Get the root directory of the project
   */
  getProjectRoot(): string {
    return process.cwd();
  },

  /**
   * Get the extensions directory path
   */
  getExtensionsDir(): string {
    return path.join(this.getProjectRoot(), 'packages', 'extensions');
  },

  /**
   * Get the shared locales directory path
   */
  getSharedLocalesDir(): string {
    return path.join(this.getProjectRoot(), 'packages', 'shared', 'locales');
  },

  /**
   * Get the output directory path
   */
  getOutputDir(): string {
    return path.join(this.getProjectRoot(), '.output');
  },

  /**
   * Get the extension-specific directory path
   */
  getExtensionDir(extensionName: string): string {
    return path.join(this.getExtensionsDir(), extensionName);
  },

  /**
   * Get the variants directory path for an extension
   */
  getVariantsDir(extensionName: string): string {
    return path.join(this.getExtensionDir(extensionName), '.variants');
  },

  /**
   * Get the manifest backup directory path for an extension
   */
  getManifestDir(extensionName: string): string {
    return path.join(this.getExtensionDir(extensionName), '.manifest');
  },

  /**
   * Get the locale files directory path for an extension
   */
  getExtensionLocalesDir(extensionName: string): string {
    return path.join(this.getExtensionDir(extensionName), 'locales');
  },

  /**
   * Get the variant-specific directory path
   */
  getVariantDir(extensionName: string, variantTarget: string): string {
    return path.join(this.getVariantsDir(extensionName), variantTarget);
  },

  /**
   * Get the Chrome _locales directory path for a variant
   */
  getChromeLocalesDir(extensionName: string, variantTarget: string): string {
    return path.join(this.getVariantDir(extensionName, variantTarget), 'public', '_locales');
  },
};

/**
 * File system utilities
 */
export const fs_utils = {
  /**
   * Ensure a directory exists, create if it doesn't
   */
  async ensureDir(dirPath: string): Promise<void> {
    try {
      await fs.ensureDir(dirPath);
      logger.verbose(`Ensured directory: ${dirPath}`);
    } catch (error) {
      logger.error(`Failed to ensure directory ${dirPath}:`, error);
      throw error;
    }
  },

  /**
   * Check if a file exists
   */
  async exists(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  },

  /**
   * Read and parse a JSON file
   */
  async readJson<T = any>(filePath: string): Promise<T> {
    try {
      const content = await fs.readFile(filePath, 'utf-8');
      return JSON.parse(content);
    } catch (error) {
      logger.error(`Failed to read JSON file ${filePath}:`, error);
      throw error;
    }
  },

  /**
   * Write JSON data to a file
   */
  async writeJson(filePath: string, data: any, options: { spaces?: number } = {}): Promise<void> {
    try {
      await this.ensureDir(path.dirname(filePath));
      const content = JSON.stringify(data, null, options.spaces ?? 2);
      await fs.writeFile(filePath, content, 'utf-8');
      logger.verbose(`Wrote JSON file: ${filePath}`);
    } catch (error) {
      logger.error(`Failed to write JSON file ${filePath}:`, error);
      throw error;
    }
  },

  /**
   * Copy a file from source to destination
   */
  async copyFile(src: string, dest: string): Promise<void> {
    try {
      await this.ensureDir(path.dirname(dest));
      await fs.copyFile(src, dest);
      logger.verbose(`Copied file: ${src} -> ${dest}`);
    } catch (error) {
      logger.error(`Failed to copy file ${src} to ${dest}:`, error);
      throw error;
    }
  },

  /**
   * Remove a file or directory
   */
  async remove(filePath: string): Promise<void> {
    try {
      await fs.remove(filePath);
      logger.verbose(`Removed: ${filePath}`);
    } catch (error) {
      logger.error(`Failed to remove ${filePath}:`, error);
      throw error;
    }
  },

  /**
   * Clean a directory (remove all contents but keep the directory)
   */
  async cleanDir(dirPath: string): Promise<void> {
    try {
      if (await this.exists(dirPath)) {
        await fs.emptyDir(dirPath);
        logger.verbose(`Cleaned directory: ${dirPath}`);
      }
    } catch (error) {
      logger.error(`Failed to clean directory ${dirPath}:`, error);
      throw error;
    }
  },
};

/**
 * String utilities
 */
export const string_utils = {
  /**
   * Convert a string to kebab-case
   */
  kebabCase(str: string): string {
    return str.replace(/[A-Z]/g, (match) => `-${match.toLowerCase()}`);
  },

  /**
   * Convert a string to camelCase
   */
  camelCase(str: string): string {
    return str.replace(/-([a-z])/g, (match, letter) => letter.toUpperCase());
  },

  /**
   * Convert a string to PascalCase
   */
  pascalCase(str: string): string {
    return str.charAt(0).toUpperCase() + this.camelCase(str.slice(1));
  },

  /**
   * Escape special characters in a string for use in regex
   */
  escapeRegex(str: string): string {
    return str.replace(/[.*+?^${}()|[\\]\\]/g, '\\$&');
  },

  /**
   * Check if a string matches any of the given patterns (supports regex)
   */
  matchesAny(str: string, patterns: string[]): boolean {
    return patterns.some((pattern) => {
      try {
        const regex = new RegExp(pattern);
        return regex.test(str);
      } catch {
        // If pattern is not a valid regex, treat as literal string
        return str === pattern;
      }
    });
  },

  /**
   * Filter strings by include/exclude patterns
   */
  filterByPatterns(
    items: string[],
    options: {
      includes?: string[];
      excludes?: string[];
    } = {}
  ): string[] {
    let filtered = [...items];

    // Apply includes filter
    if (options.includes && options.includes.length > 0) {
      filtered = filtered.filter((item) => this.matchesAny(item, options.includes!));
    }

    // Apply excludes filter
    if (options.excludes && options.excludes.length > 0) {
      filtered = filtered.filter((item) => !this.matchesAny(item, options.excludes!));
    }

    return filtered;
  },
};

/**
 * Array utilities
 */
export const array_utils = {
  /**
   * Remove duplicates from an array
   */
  unique<T>(arr: T[]): T[] {
    return [...new Set(arr)];
  },

  /**
   * Group array items by a key function
   */
  groupBy<T, K extends string | number | symbol>(
    arr: T[],
    keyFn: (item: T) => K
  ): Record<K, T[]> {
    return arr.reduce((groups, item) => {
      const key = keyFn(item);
      if (!groups[key]) {
        groups[key] = [];
      }
      groups[key].push(item);
      return groups;
    }, {} as Record<K, T[]>);
  },

  /**
   * Check if two arrays are equal
   */
  isEqual<T>(arr1: T[], arr2: T[]): boolean {
    if (arr1.length !== arr2.length) return false;
    return arr1.every((item, index) => item === arr2[index]);
  },

  /**
   * Flatten nested arrays
   */
  flatten<T>(arr: (T | T[])[]): T[] {
    return arr.reduce((flat: T[], item) => {
      if (Array.isArray(item)) {
        flat.push(...this.flatten(item));
      } else {
        flat.push(item);
      }
      return flat;
    }, []);
  },
};

/**
 * Object utilities
 */
export const object_utils = {
  /**
   * Deep merge objects
   */
  deepMerge<T extends Record<string, any>>(target: T, ...sources: Partial<T>[]): T {
    if (!sources.length) return target;
    const source = sources.shift();

    if (this.isObject(target) && this.isObject(source)) {
      for (const key in source) {
        if (this.isObject(source[key])) {
          if (!target[key]) Object.assign(target, { [key]: {} });
          this.deepMerge(target[key], source[key]);
        } else {
          Object.assign(target, { [key]: source[key] });
        }
      }
    }

    return this.deepMerge(target, ...sources);
  },

  /**
   * Deep clone an object
   */
  deepClone<T>(obj: T): T {
    return JSON.parse(JSON.stringify(obj));
  },

  /**
   * Check if a value is an object
   */
  isObject(item: any): boolean {
    return item && typeof item === 'object' && !Array.isArray(item);
  },

  /**
   * Get a nested property value using dot notation
   */
  get(obj: any, path: string, defaultValue?: any): any {
    const keys = path.split('.');
    let result = obj;
    
    for (const key of keys) {
      if (result === null || result === undefined) {
        return defaultValue;
      }
      result = result[key];
    }
    
    return result !== undefined ? result : defaultValue;
  },

  /**
   * Set a nested property value using dot notation
   */
  set(obj: any, path: string, value: any): void {
    const keys = path.split('.');
    let current = obj;
    
    for (let i = 0; i < keys.length - 1; i++) {
      const key = keys[i];
      if (!(key in current) || !this.isObject(current[key])) {
        current[key] = {};
      }
      current = current[key];
    }
    
    current[keys[keys.length - 1]] = value;
  },

  /**
   * Check if an object has a nested property using dot notation
   */
  has(obj: any, path: string): boolean {
    const keys = path.split('.');
    let current = obj;
    
    for (const key of keys) {
      if (current === null || current === undefined || !(key in current)) {
        return false;
      }
      current = current[key];
    }
    
    return true;
  },

  /**
   * Pick specific properties from an object
   */
  pick<T, K extends keyof T>(obj: T, keys: K[]): Pick<T, K> {
    const result = {} as Pick<T, K>;
    keys.forEach((key) => {
      if (key in obj) {
        result[key] = obj[key];
      }
    });
    return result;
  },

  /**
   * Omit specific properties from an object
   */
  omit<T, K extends keyof T>(obj: T, keys: K[]): Omit<T, K> {
    const result = { ...obj } as any;
    keys.forEach((key) => {
      delete result[key];
    });
    return result;
  },
};

/**
 * Validation utilities
 */
export const validation_utils = {
  /**
   * Check if a value is defined and not null
   */
  isDefined<T>(value: T | undefined | null): value is T {
    return value !== undefined && value !== null;
  },

  /**
   * Check if a string is not empty
   */
  isNonEmptyString(value: any): value is string {
    return typeof value === 'string' && value.trim().length > 0;
  },

  /**
   * Check if an array is not empty
   */
  isNonEmptyArray(value: any): value is any[] {
    return Array.isArray(value) && value.length > 0;
  },

  /**
   * Check if a value is a valid semantic version
   */
  isValidSemver(value: string): boolean {
    const semverRegex = /^(\d+)\.(\d+)\.(\d+)(?:-([a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*))?(?:\+([a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*))?$/;
    return semverRegex.test(value);
  },

  /**
   * Check if a value is a valid locale code
   */
  isValidLocale(value: string): boolean {
    const localeRegex = /^[a-z]{2}(_[A-Z]{2})?$/;
    return localeRegex.test(value);
  },

  /**
   * Check if a value is a valid variant target format
   */
  isValidVariantTarget(value: string): boolean {
    const variantTargetRegex = /^[a-z0-9]+-(mv[23])-[a-z]+$/;
    return variantTargetRegex.test(value);
  },
};

/**
 * File globbing utilities
 */
export const glob_utils = {
  /**
   * Find files matching a pattern
   */
  async findFiles(pattern: string, options: { cwd?: string; absolute?: boolean } = {}): Promise<string[]> {
    try {
      const files = await glob(pattern, {
        cwd: options.cwd || process.cwd(),
        absolute: options.absolute ?? false,
      });
      logger.verbose(`Found ${files.length} files matching pattern: ${pattern}`);
      return files;
    } catch (error) {
      logger.error(`Failed to find files with pattern ${pattern}:`, error);
      throw error;
    }
  },

  /**
   * Find locale files in a directory
   */
  async findLocaleFiles(dirPath: string): Promise<string[]> {
    const pattern = path.join(dirPath, '*.json');
    const files = await this.findFiles(pattern, { absolute: true });
    return files.filter((file) => {
      const basename = path.basename(file, '.json');
      return validation_utils.isValidLocale(basename);
    });
  },

  /**
   * Find extension directories
   */
  async findExtensionDirs(): Promise<string[]> {
    const extensionsDir = paths.getExtensionsDir();
    const pattern = path.join(extensionsDir, '*');
    const items = await this.findFiles(pattern, { absolute: true });
    
    // Filter to only include directories
    const dirs = [];
    for (const item of items) {
      const stats = await fs.stat(item);
      if (stats.isDirectory()) {
        dirs.push(item);
      }
    }
    
    return dirs;
  },
};

/**
 * Performance utilities
 */
export const perf_utils = {
  /**
   * Measure execution time of a function
   */
  async measureTime<T>(fn: () => Promise<T>, label?: string): Promise<{ result: T; duration: number }> {
    const start = performance.now();
    const result = await fn();
    const duration = performance.now() - start;
    
    if (label) {
      logger.verbose(`${label} took ${duration.toFixed(2)}ms`);
    }
    
    return { result, duration };
  },

  /**
   * Debounce a function
   */
  debounce<T extends (...args: any[]) => any>(
    fn: T,
    delay: number
  ): (...args: Parameters<T>) => void {
    let timeoutId: NodeJS.Timeout;
    return (...args: Parameters<T>) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => fn(...args), delay);
    };
  },

  /**
   * Throttle a function
   */
  throttle<T extends (...args: any[]) => any>(
    fn: T,
    delay: number
  ): (...args: Parameters<T>) => void {
    let lastExecTime = 0;
    return (...args: Parameters<T>) => {
      const currentTime = Date.now();
      if (currentTime - lastExecTime >= delay) {
        fn(...args);
        lastExecTime = currentTime;
      }
    };
  },
};

/**
 * Export all utilities as a single object
 */
export const utils = {
  paths,
  fs: fs_utils,
  string: string_utils,
  array: array_utils,
  object: object_utils,
  validation: validation_utils,
  glob: glob_utils,
  perf: perf_utils,
};