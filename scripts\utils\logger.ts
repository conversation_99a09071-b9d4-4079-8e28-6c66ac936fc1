import { consola } from 'consola';

/**
 * @description 统一的日志管理器
 * 基于 consola 提供格式化的日志输出，支持上下文分组和不同级别的日志
 */
export class Logger {
  private context: string;
  private static verboseMode: boolean = false;

  constructor(context: string = 'App') {
    this.context = context;
  }

  /**
   * @description 创建带上下文的日志实例
   * @param context 上下文名称，用于标识日志来源
   */
  static create(context: string): Logger {
    return new Logger(context);
  }

  /**
   * @description 设置全局 verbose 模式
   * @param enabled 是否启用 verbose 模式
   */
  static setVerbose(enabled: boolean): void {
    Logger.verboseMode = enabled;
  }

  /**
   * @description 获取当前 verbose 模式状态
   */
  static isVerbose(): boolean {
    return Logger.verboseMode;
  }

  /**
   * @description 输出信息级别日志
   */
  info(message: string, ...args: unknown[]): void {
    consola.info(`[${this.context}] ${message}`, ...args);
  }

  /**
   * @description 输出成功级别日志
   */
  success(message: string, ...args: unknown[]): void {
    consola.success(`[${this.context}] ${message}`, ...args);
  }

  /**
   * @description 输出警告级别日志
   */
  warn(message: string, ...args: unknown[]): void {
    consola.warn(`[${this.context}] ${message}`, ...args);
  }

  /**
   * @description 输出错误级别日志
   */
  error(message: string, ...args: unknown[]): void {
    consola.error(`[${this.context}] ${message}`, ...args);
  }

  /**
   * @description 输出调试级别日志
   */
  debug(message: string, ...args: unknown[]): void {
    consola.debug(`[${this.context}] ${message}`, ...args);
  }

  /**
   * @description 输出详细信息日志（仅在 verbose 模式下显示）
   */
  verbose(message: string, ...args: unknown[]): void {
    if (Logger.verboseMode) {
      consola.info(`[${this.context}] ${message}`, ...args);
    }
  }

  /**
   * @description 开始一个日志组
   */
  group(title: string): void {
    consola.log(`\n📁 ${title}`);
  }

  /**
   * @description 结束当前日志组
   */
  groupEnd(): void {
    consola.log('');
  }

  /**
   * @description 输出进度信息
   */
  progress(message: string, current?: number, total?: number): void {
    if (current !== undefined && total !== undefined) {
      const percentage = Math.round((current / total) * 100);
      consola.info(`[${this.context}] ${message} (${current}/${total} - ${percentage}%)`);
    } else {
      consola.info(`[${this.context}] ${message}`);
    }
  }

  /**
   * @description 输出表格数据
   */
  table(data: Record<string, unknown>[]): void {
    console.table(data);
  }
}

/**
 * @description 默认的全局日志实例
 */
export const logger = Logger.create('Global');

/**
 * @description 创建特定上下文的日志实例
 */
export const createLogger = (context: string): Logger => Logger.create(context);
