# 配置管理模块开发计划

**项目**: 浏览器插件脚手架 - 配置管理模块 (`scripts/extension-config`)
**开始时间**: 2025-07-10
**状态**: 进行中

## 概述

开发专注于配置管理的核心模块，负责处理用户配置 (`extension.config.ts`) 并为下游模块提供标准化的配置数据。严格遵循数据与I/O分离原则，只处理配置逻辑，不涉及具体的 i18n、manifest 处理和文件生成。

## 核心目标

- [ ] 实现 `defineExtensionConfig` 函数，专注于配置处理和规范化
- [ ] 建立完整的 TypeScript 类型系统，定义标准配置接口
- [ ] 实现配置校验和错误处理机制
- [ ] 为下游模块 (`i18n`, `manifest`) 提供标准化的配置数据
- [ ] 确保插件和渠道包基本信息的准确性和完整性

## 模块职责边界

### ✅ 负责的功能
- 配置数据的合并、校验和规范化
- 插件和渠道包基本信息的确定
- 为下游模块提供标准化的配置数据结构
- 配置相关的类型定义和接口

### ❌ 不负责的功能
- i18n 具体处理（语言文件扫描、文案转换等）
- manifest 具体处理（manifest.json 生成等）
- 文件系统操作（文件读写、目录创建等）
- 构建流程控制

## 技术栈

- **核心语言**: TypeScript
- **依赖包**: lodash-es (深度合并), consola (日志)
- **代码复用**: scripts/utils/logger.ts
- **测试框架**: vitest (后续)

## 开发阶段

### 第一阶段：基础架构 [x]

#### 1.1 类型定义 (`types.ts`) [x]
- [x] 定义 `UserExtensionConfig` 接口
- [x] 定义 `ProcessedExtensionConfig` 接口
- [x] 定义 `VariantConfig` 接口
- [x] 定义 `ProcessedVariantConfig` 接口
- [x] 定义辅助类型和枚举

#### 1.2 常量定义 (`constants.ts`) [x]
- [x] 定义默认配置值
- [x] 定义 webstore 到 webstoreCN 的映射表
- [x] 定义 variantType 相关常量
- [x] 定义默认的 i18n 配置

#### 1.3 工具函数 (`utils.ts`) [x]
- [x] 实现 `generateVariantTarget` 函数
- [x] 实现 `generateVariantChannel` 函数
- [x] 实现 `generateWebstoreCN` 函数
- [x] 实现配置字段规范化函数

### 第二阶段：核心处理逻辑 [x]

#### 2.1 配置处理器 (`processors.ts`) [x]
- [x] 实现 `processVariantConfig` 函数
- [x] 实现 `mergeConfigurations` 函数
- [x] 实现 `normalizeVariantFields` 函数
- [x] 实现 `fillDefaultValues` 函数

#### 2.2 配置校验器 (`validators.ts`) [x]
- [x] 实现 `validateUserConfig` 函数
- [x] 实现 `validateVariantConfig` 函数
- [x] 实现 `checkUniqueVariantTargets` 函数
- [x] 实现 `validateRequiredFields` 函数

### 第三阶段：主入口集成 [x]

#### 3.1 主入口函数 (`index.ts`) [x]
- [x] 实现 `defineExtensionConfig` 主函数
- [x] 集成配置校验流程
- [x] 集成配置处理流程
- [x] 添加错误处理和日志输出
- [x] 导出所有公共接口

### 第四阶段：上层功能接口 [ ]

在核心配置处理能力之上，构建面向开发和构建脚本的实用高级 API。

#### 4.1 查询接口 [ ]
- [ ] 实现 `listExtensionVariants(options?: { extensionNames?: string[] })`
  - **功能**: 列出所有或指定的插件及其渠道包信息。
  - **细节**:
    - `extensionNames` 为空时，扫描 `packages/extensions` 目录，查找所有插件。
    - 动态加载每个插件的 `extension.config.ts`。
    - 返回处理后的配置，但会**过滤掉** `i18n` 和 `manifest` 字段，使其轻量化，适合展示和选择。

#### 4.2 配置生成接口 [ ]
- [ ] 实现 `generateExtensionConfigs(options: { extensionName: string; variantNames?: string[] })`
  - **功能**: 为指定的插件和一个或多个渠道包生成完整的、可用于构建的配置数据。
  - **细节**:
    - `variantNames` 为空时，处理该插件下的所有渠道包。
    - 调用核心处理器 (`defineExtensionConfig`) 获取基础配置。
    - **编排**：调用 `scripts/i18n` 和 `scripts/manifest` 的辅助函数，填充 `i18n` 和 `manifest` 的具体内容。
    - 返回一个包含所有完整信息的对象数组，每个对象对应一个渠道包。

#### 4.3 文件写入接口 [ ]
- [ ] 实现 `writeExtensionConfigs(configs: FullVariantConfig[])`
  - **功能**: 将 `generateExtensionConfigs` 生成的数据写入到文件系统。
  - **细节**:
    - **输入**: `generateExtensionConfigs` 的返回结果。
    - **输出**:
      - `.variants/{variantId}/extension.json`: 过滤掉 i18n 和 manifest 的核心配置。
      - `.variants/{variantId}/i18n.json`: 供 Vue 等前端框架使用的语言包。
      - `public/_locales/{locale}/messages.json`: 供浏览器扩展 API 使用的 `messages.json` 文件。

### 第五阶段：测试和优化 [ ]

#### 5.1 单元测试 [ ]
- [ ] 为核心处理函数编写测试
- [ ] 测试配置校验逻辑
- [ ] 测试错误处理场景
- [ ] 测试边界情况

#### 5.2 集成测试 [ ]
- [ ] 测试与现有 `extension.config.ts` 的兼容性
- [ ] 测试生成的配置数据格式
- [ ] 验证与下游模块的集成
- [ ] 测试上层功能接口 (`list`, `generate`, `write`)

#### 5.3 文档和注释 [ ]
- [ ] 添加详细的 JSDoc 注释（中文）
- [ ] 更新 README 或相关文档
- [ ] 添加使用示例

## 实现细节

### 数据流设计
```
用户配置 (UserExtensionConfig)
    ↓ defineExtensionConfig()
配置校验 (validators)
    ↓
配置处理 (processors)
    ↓
标准化配置数据 (ProcessedExtensionConfig)
    ↓
传递给下游模块:
  - i18n 模块 (专门处理国际化)
  - manifest 模块 (专门处理清单文件)
  - build 模块 (文件生成和构建)
```

### 关键接口设计
- `defineExtensionConfig(config: UserExtensionConfig): ProcessedExtensionConfig`
- 每个 variant 包含完整的基本配置信息，实现数据自包含
- 支持全局配置与 variant 配置的深度合并
- 提供标准化的配置数据结构，供下游模块使用

### 错误处理策略
- 使用 TypeScript 类型系统进行编译时检查
- 运行时校验关键配置项
- 提供清晰的错误信息和修复建议
- 使用 consola 统一日志格式

## 质量标准

- **类型安全**: 充分利用 TypeScript 类型系统
- **代码风格**: 遵循项目 ESLint 和 Prettier 配置
- **注释规范**: 使用中文 JSDoc 风格注释
- **模块化**: 每个功能模块职责清晰，可独立测试
- **可维护性**: 代码结构清晰，易于理解和修改

## 完成标准

- [ ] 所有类型定义完整且准确
- [ ] 核心处理逻辑通过所有测试用例
- [ ] 与现有代码完全兼容
- [ ] 错误处理覆盖所有异常场景
- [ ] 代码通过所有质量检查（lint, format, compile）

---

**更新日志**:
- 2025-07-10: 创建开发计划文档
- 2025-07-10: 明确模块职责边界，专注于配置管理，不涉及 i18n/manifest 具体处理
- 2025-07-10: ✅ 完成第一阶段：基础架构（types.ts, constants.ts, utils.ts）
- 2025-07-10: ✅ 完成第二阶段：核心处理逻辑（processors.ts, validators.ts）
- 2025-07-10: ✅ 完成第三阶段：主入口集成（index.ts）
- 2025-07-10: 📝 核心功能实现完成，代码无语法错误，准备进入测试阶段
